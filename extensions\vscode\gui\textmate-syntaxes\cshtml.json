{"name": "ASP.NET Razor", "scopeName": "text.html.cshtml", "fileTypes": ["cshtml", "gohtml"], "patterns": [{"name": "section.embedded.source.cshtml", "begin": "(@?([a-zA-Z0-9]+)?)(\\s[a-zA-Z0-9]+)?(\n|\r)?(\\{)", "beginCaptures": {"0": {"name": "punctuation.section.embedded.begin.cshtml"}, "1": {"name": "keyword.control.cshtml"}}, "patterns": [{"name": "section.embedded.source.cshtml", "begin": "(@?([a-zA-Z0-9]+)?)(\\s[a-zA-Z0-9]+)?(\n|\r)?(\\{)", "beginCaptures": {"0": {"name": "punctuation.section.embedded.begin.cshtml"}, "1": {"name": "keyword.control.cshtml"}}, "patterns": [{"name": "string.quoted.single.cshtml", "match": "'"}, {"include": "#embedded-code"}, {"include": "#comments"}, {"include": "source.cs"}, {"include": "text.html.basic"}], "end": "\\}", "endCaptures": {"0": {"name": "punctuation.section.embedded.begin.cshtml"}}}, {"name": "string.quoted.single.cshtml", "match": "'"}, {"include": "#embedded-code"}, {"include": "#comments"}, {"include": "text.html.basic"}], "end": "\\}", "endCaptures": {"0": {"name": "punctuation.section.embedded.begin.cshtml"}}, "comments": "Simple multi-line code section"}, {"begin": "(@[a-zA-Z0-9]+)(\\s?)", "captures": {"0": {"name": "section.embedded.begin.cshtml"}, "1": {"name": "keyword.control.cshtml"}}, "patterns": [{"match": "(([a-zA-Z0-9]+)(\\.)?)+?", "captures": {"2": {"name": "entity.name.tag.source.cshtml"}, "3": {"name": "punctuation.separator.namespace.source.cshtml"}}}, {"include": "#embedded-code"}, {"include": "#comments"}, {"include": "source.cs"}, {"include": "text.html.basic"}], "end": "(\\n|\\s)", "comments": "Covers single line Razor tags"}, {"include": "#comments"}, {"include": "text.html.basic"}], "repository": {"embedded-code": {"match": "(@?[a-zA-Z0-9]+)(\\.([a-zA-Z0-9]+))?", "captures": {"1": {"name": "keyword.control.cshtml"}, "3": {"name": "entity.name.tag.source.cshtml"}}, "patterns": [{"include": "#comments"}]}, "comments": {"begin": "@\\*", "captures": {"0": {"name": "punctuation.definition.comment.source.cshtml"}}, "end": "\\*@", "name": "comment.block.cshtml"}}, "version": "https://github.com/demyte/language-cshtml/commit/a49735dc7aef56ae772a3bcfd8e42c89895dcff4"}