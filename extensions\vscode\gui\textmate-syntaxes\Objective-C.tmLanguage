<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>m</string>
		<string>h</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~O</string>
	<key>name</key>
	<string>Objective-C</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>((@)(interface|protocol))(?!.+;)\s+([A-Za-z_][A-Za-z0-9_]*)\s*((:)(?:\s*)([A-Za-z][A-Za-z0-9]*))?(\s|\n)?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.objc</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.objc</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.entity.other.inherited-class.objc</string>
				</dict>
				<key>7</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.objc</string>
				</dict>
				<key>8</key>
				<dict>
					<key>name</key>
					<string>meta.divider.objc</string>
				</dict>
				<key>9</key>
				<dict>
					<key>name</key>
					<string>meta.inherited-class.objc</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>meta.scope.interface.objc</string>
			<key>end</key>
			<string>((@)end)\b</string>
			<key>name</key>
			<string>meta.interface-or-protocol.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interface_innards</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>((@)(implementation))\s+([A-Za-z_][A-Za-z0-9_]*)\s*(?::\s*([A-Za-z][A-Za-z0-9]*))?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.objc</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.objc</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.objc</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>meta.scope.implementation.objc</string>
			<key>end</key>
			<string>((@)end)\b</string>
			<key>name</key>
			<string>meta.implementation.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#implementation_innards</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>applyEndPatternLast</key>
			<true/>
			<key>begin</key>
			<string>(?=@")</string>
			<key>end</key>
			<string>(?=\S)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>@?"</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>"</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.objc</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.double.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.c#string_escaped_char</string>
						</dict>
						<dict>
							<key>match</key>
							<string>(?x)%
    						(\d+\$)?                             # field (argument #)
    						[#0\- +']*                          # flags
    						((-?\d+)|\*(-?\d+\$)?)?              # minimum field width
    						(\.((-?\d+)|\*(-?\d+\$)?)?)?         # precision
    						[@]                                  # conversion type
    					</string>
							<key>name</key>
							<string>constant.other.placeholder.objc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.c#string_placeholder</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(id)\s*(?=&lt;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.objc</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?&lt;=&gt;)</string>
			<key>name</key>
			<string>meta.id-with-protocol.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#protocol_list</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(NS_DURING|NS_HANDLER|NS_ENDHANDLER)\b</string>
			<key>name</key>
			<string>keyword.control.macro.objc</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(try|catch|finally|throw)\b</string>
			<key>name</key>
			<string>keyword.control.exception.objc</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(synchronized)\b</string>
			<key>name</key>
			<string>keyword.control.synchronize.objc</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(required|optional)\b</string>
			<key>name</key>
			<string>keyword.control.protocol-specification.objc</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(defs|encode)\b</string>
			<key>name</key>
			<string>keyword.other.objc</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(class|protocol)\b</string>
			<key>name</key>
			<string>storage.type.objc</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>((@)selector)\s*(\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.objc</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>meta.selector.method-name.objc</string>
			<key>end</key>
			<string>(\))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.objc</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.selector.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.arguments.objc</string>
						</dict>
					</dict>
					<key>match</key>
					<string>\b(?:[a-zA-Z_:][\w]*)+</string>
					<key>name</key>
					<string>support.function.any-method.name-of-parameter.objc</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.modifier.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(synchronized|public|package|private|protected)\b</string>
			<key>name</key>
			<string>storage.modifier.objc</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(YES|NO|Nil|nil)\b</string>
			<key>name</key>
			<string>constant.language.objc</string>
		</dict>
		<dict>
			<key>include</key>
			<string>source.objc.platform</string>
		</dict>
		<dict>
			<key>include</key>
			<string>source.c</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#bracketed_content</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>bracketed_content</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.begin.objc</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.end.objc</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.bracketed.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?=predicateWithFormat:)(?&lt;=NSPredicate )(predicateWithFormat:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.any-method.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.arguments.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\])</string>
					<key>name</key>
					<string>meta.function-call.predicate.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.arguments.objc</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\bargument(Array|s)(:)</string>
							<key>name</key>
							<string>support.function.any-method.name-of-parameter.objc</string>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.arguments.objc</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\b\w+(:)</string>
							<key>name</key>
							<string>invalid.illegal.unknown-method.objc</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>@"</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.string.begin.objc</string>
								</dict>
							</dict>
							<key>end</key>
							<string>"</string>
							<key>endCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.string.end.objc</string>
								</dict>
							</dict>
							<key>name</key>
							<string>string.quoted.double.objc</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\b(AND|OR|NOT|IN)\b</string>
									<key>name</key>
									<string>keyword.operator.logical.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\b(ALL|ANY|SOME|NONE)\b</string>
									<key>name</key>
									<string>constant.language.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\b(NULL|NIL|SELF|TRUE|YES|FALSE|NO|FIRST|LAST|SIZE)\b</string>
									<key>name</key>
									<string>constant.language.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\b(MATCHES|CONTAINS|BEGINSWITH|ENDSWITH|BETWEEN)\b</string>
									<key>name</key>
									<string>keyword.operator.comparison.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\bC(ASEINSENSITIVE|I)\b</string>
									<key>name</key>
									<string>keyword.other.modifier.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\b(ANYKEY|SUBQUERY|CAST|TRUEPREDICATE|FALSEPREDICATE)\b</string>
									<key>name</key>
									<string>keyword.other.predicate.cocoa</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\\(\\|[abefnrtv'"?]|[0-3]\d{0,2}|[4-7]\d?|x[a-zA-Z0-9]+)</string>
									<key>name</key>
									<string>constant.character.escape.objc</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\\.</string>
									<key>name</key>
									<string>invalid.illegal.unknown-escape.objc</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#special_variables</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#c_functions</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$base</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=\w)(?&lt;=[\w\])"] )(\w+(?:(:)|(?=\])))</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.any-method.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.arguments.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\])</string>
					<key>name</key>
					<string>meta.function-call.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.arguments.objc</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\b\w+(:)</string>
							<key>name</key>
							<string>support.function.any-method.name-of-parameter.objc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#special_variables</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#c_functions</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$base</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#special_variables</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#c_functions</string>
				</dict>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>c_functions</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.c.platform#functions</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.whitespace.function-call.leading.c</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.function.any-method.c</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.c</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?x) (?: (?= \s )  (?:(?&lt;=else|new|return) | (?&lt;!\w)) (\s+))?
            			(\b
            				(?!(while|for|do|if|else|switch|catch|enumerate|return|r?iterate)\s*\()(?:(?!NS)[A-Za-z_][A-Za-z0-9_]*+\b | :: )++                  # actual name
            			)
            			 \s*(\()</string>
					<key>name</key>
					<string>meta.function-call.c</string>
				</dict>
			</array>
		</dict>
		<key>comment</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>/\*</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\*/</string>
					<key>name</key>
					<string>comment.block.objc</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(^[ \t]+)?(?=//)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.whitespace.comment.leading.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?!\G)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>//</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.objc</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\n</string>
							<key>name</key>
							<string>comment.line.double-slash.objc</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>(?&gt;\\\s*\n)</string>
									<key>name</key>
									<string>punctuation.separator.continuation.objc</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>disabled</key>
		<dict>
			<key>begin</key>
			<string>^\s*#\s*if(n?def)?\b.*$</string>
			<key>comment</key>
			<string>eat nested preprocessor if(def)s</string>
			<key>end</key>
			<string>^\s*#\s*endif\b.*$</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#disabled</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#pragma-mark</string>
				</dict>
			</array>
		</dict>
		<key>implementation_innards</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-enabled-implementation</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-disabled-implementation</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-other-implementation</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#property_directive</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#special_variables</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#method_super</string>
				</dict>
				<dict>
					<key>include</key>
					<string>$base</string>
				</dict>
			</array>
		</dict>
		<key>interface_innards</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-enabled-interface</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-disabled-interface</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#preprocessor-rule-other-interface</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#properties</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#protocol_list</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#method</string>
				</dict>
				<dict>
					<key>include</key>
					<string>$base</string>
				</dict>
			</array>
		</dict>
		<key>method</key>
		<dict>
			<key>begin</key>
			<string>^(-|\+)\s*</string>
			<key>end</key>
			<string>(?=\{|#)|;</string>
			<key>name</key>
			<string>meta.function.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.type.begin.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))\s*(\w+\b)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.type.end.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.objc</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.return-type.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#protocol_list</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#protocol_type_qualifier</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$base</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>\b\w+(?=:)</string>
					<key>name</key>
					<string>entity.name.function.name-of-parameter.objc</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>((:))\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.name-of-parameter.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.arguments.objc</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.type.begin.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))\s*(\w+\b)?</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.type.end.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>variable.parameter.function.objc</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.argument-type.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#protocol_list</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#protocol_type_qualifier</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$base</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#comment</string>
				</dict>
			</array>
		</dict>
		<key>method_super</key>
		<dict>
			<key>begin</key>
			<string>^(?=-|\+)</string>
			<key>end</key>
			<string>(?&lt;=\})|(?=#)</string>
			<key>name</key>
			<string>meta.function-with-body.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#method</string>
				</dict>
				<dict>
					<key>include</key>
					<string>$base</string>
				</dict>
			</array>
		</dict>
		<key>pragma-mark</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.pragma.c</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>meta.toc-list.pragma-mark.c</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(#\s*(pragma\s+mark)\s+(.*))</string>
			<key>name</key>
			<string>meta.section</string>
		</dict>
		<key>preprocessor-rule-disabled-implementation</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#(if)\s+(0)\b).*</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.if.c</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.preprocessor.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*(#\s*(else)\b)</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.preprocessor.c</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.import.else.c</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=^\s*#\s*endif\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#interface_innards</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string></string>
					<key>end</key>
					<string>(?=^\s*#\s*(else|endif)\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>name</key>
					<string>comment.block.preprocessor.if-branch.c</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#disabled</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#pragma-mark</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>preprocessor-rule-disabled-interface</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#(if)\s+(0)\b).*</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.if.c</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.preprocessor.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*(#\s*(else)\b)</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.preprocessor.c</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.import.else.c</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=^\s*#\s*endif\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#interface_innards</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string></string>
					<key>end</key>
					<string>(?=^\s*#\s*(else|endif)\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>name</key>
					<string>comment.block.preprocessor.if-branch.c</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#disabled</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#pragma-mark</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>preprocessor-rule-enabled-implementation</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#(if)\s+(0*1)\b)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.if.c</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.preprocessor.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*(#\s*(else)\b).*</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.preprocessor.c</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.import.else.c</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>comment.block.preprocessor.else-branch.c</string>
					<key>end</key>
					<string>(?=^\s*#\s*endif\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#disabled</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#pragma-mark</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string></string>
					<key>end</key>
					<string>(?=^\s*#\s*(else|endif)\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#implementation_innards</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>preprocessor-rule-enabled-interface</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#(if)\s+(0*1)\b)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.if.c</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.preprocessor.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*(#\s*(else)\b).*</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.preprocessor.c</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.import.else.c</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>comment.block.preprocessor.else-branch.c</string>
					<key>end</key>
					<string>(?=^\s*#\s*endif\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#disabled</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#pragma-mark</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string></string>
					<key>end</key>
					<string>(?=^\s*#\s*(else|endif)\b.*?(?:(?=(?://|/\*))|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#interface_innards</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>preprocessor-rule-other-implementation</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#\s*(if(n?def)?)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b).*?(?:(?=(?://|/\*))|$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#implementation_innards</string>
				</dict>
			</array>
		</dict>
		<key>preprocessor-rule-other-interface</key>
		<dict>
			<key>begin</key>
			<string>^\s*(#\s*(if(n?def)?)\b.*?(?:(?=(?://|/\*))|$))</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.preprocessor.c</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.import.c</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\s*(#\s*(endif)\b).*?(?:(?=(?://|/\*))|$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interface_innards</string>
				</dict>
			</array>
		</dict>
		<key>properties</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>((@)property)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.property.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.keyword.objc</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.scope.begin.objc</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.scope.end.objc</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.property-with-attributes.objc</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\b(getter|setter|readonly|readwrite|assign|retain|copy|atomic|nonatomic|strong|weak)\b</string>
							<key>name</key>
							<string>keyword.other.property.attribute</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.property.objc</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.keyword.objc</string>
						</dict>
					</dict>
					<key>match</key>
					<string>((@)property)\b</string>
					<key>name</key>
					<string>meta.property.objc</string>
				</dict>
			</array>
		</dict>
		<key>property_directive</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.objc</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)(dynamic|synthesize)\b</string>
			<key>name</key>
			<string>keyword.other.property.directive.objc</string>
		</dict>
		<key>protocol_list</key>
		<dict>
			<key>begin</key>
			<string>(&lt;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.begin.objc</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(&gt;)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.end.objc</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.protocol-list.objc</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.objc.platform#protocols</string>
				</dict>
			</array>
		</dict>
		<key>protocol_type_qualifier</key>
		<dict>
			<key>match</key>
			<string>\b(in|out|inout|oneway|bycopy|byref)\b</string>
			<key>name</key>
			<string>storage.modifier.protocol.objc</string>
		</dict>
		<key>special_variables</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b_cmd\b</string>
					<key>name</key>
					<string>variable.other.selector.objc</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(self|super)\b</string>
					<key>name</key>
					<string>variable.language.objc</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.objc</string>
	<key>uuid</key>
	<string>F85CC716-6B1C-11D9-9A20-000D93589AF6</string>
</dict>
</plist>