<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>R</string>
		<string>r</string>
		<string>s</string>
		<string>S</string>
		<string>Rprofile</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~R</string>
	<key>name</key>
	<string>R</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>comment.line.pragma.r</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.pragma.name.r</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(#pragma[ \t]+mark)[ \t](.*)</string>
			<key>name</key>
			<string>comment.line.pragma-mark.r</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=#)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.r</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>#</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.r</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.number-sign.r</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(logical|numeric|character|complex|matrix|array|data\.frame|list|factor)(?=\s*\()</string>
			<key>name</key>
			<string>storage.type.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(function|if|break|next|repeat|else|for|return|switch|while|in|invisible)\b</string>
			<key>name</key>
			<string>keyword.control.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\.?[0-9]*)|(\.[0-9]+))((e|E)(\+|-)?[0-9]+)?)(i|L|l|UL|ul|u|U|F|f|ll|LL|ull|ULL)?\b</string>
			<key>name</key>
			<string>constant.numeric.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(T|F|TRUE|FALSE|NULL|NA|Inf|NaN)\b</string>
			<key>name</key>
			<string>constant.language.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(pi|letters|LETTERS|month\.abb|month\.name)\b</string>
			<key>name</key>
			<string>support.constant.misc.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(\-|\+|\*|\/|%\/%|%%|%\*%|%in%|%o%|%x%|\^)</string>
			<key>name</key>
			<string>keyword.operator.arithmetic.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(=|&lt;-|&lt;&lt;-|-&gt;|-&gt;&gt;)</string>
			<key>name</key>
			<string>keyword.operator.assignment.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(==|!=|&lt;&gt;|&lt;|&gt;|&lt;=|&gt;=)</string>
			<key>name</key>
			<string>keyword.operator.comparison.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(!|&amp;{1,2}|[|]{1,2})</string>
			<key>name</key>
			<string>keyword.operator.logical.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(\.\.\.|\$|@|:|\~)</string>
			<key>name</key>
			<string>keyword.other.r</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.r</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.r</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.r</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.r</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.r</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.r</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.r</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.r</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.r</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.assignment.r</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.r</string>
				</dict>
			</dict>
			<key>match</key>
			<string>([[:alpha:].][[:alnum:]._]*)\s*(&lt;-)\s*(function)</string>
			<key>name</key>
			<string>meta.function.r</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.tag.r</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.r</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(setMethod|setReplaceMethod|setGeneric|setGroupGeneric|setClass)\s*\(\s*([[:alpha:]\d]+\s*=\s*)?("|\x{27})([a-zA-Z._\[\$@][a-zA-Z0-9._\[]*?)\3.*</string>
			<key>name</key>
			<string>meta.method.declaration.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>([[:alpha:].][[:alnum:]._]*)\s*\(</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.parameter.r</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.assignment.r</string>
				</dict>
			</dict>
			<key>match</key>
			<string>([[:alpha:].][[:alnum:]._]*)\s*(=)(?=[^=])</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b([\d_][[:alnum:]._]+)\b</string>
			<key>name</key>
			<string>invalid.illegal.variable.other.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b([[:alnum:]_]+)(?=::)</string>
			<key>name</key>
			<string>entity.namespace.r</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b([[:alnum:]._]+)\b</string>
			<key>name</key>
			<string>variable.other.r</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.block.begin.r</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.block.end.r</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.block.r</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.r</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>scopeName</key>
	<string>source.r</string>
	<key>uuid</key>
	<string>B2E6B78D-6E70-11D9-A369-000D93B3A10E</string>
</dict>
</plist>