<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>sql</string>
		<string>ddl</string>
		<string>dml</string>
		<string>dsql</string>
		<string>psql</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~S</string>
	<key>name</key>
	<string>SQL</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#comments</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.create.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.other.sql</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:^\s*(create(?:\s+or\s+replace)?)\s+(aggregate|conversion|database|domain|function|group|(unique\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\s+)(['"`]?)(\w+)\4</string>
			<key>name</key>
			<string>meta.create.sql</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.create.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.other.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:^\s*(drop)\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))</string>
			<key>name</key>
			<string>meta.drop.sql</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.create.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.other.table.sql</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.sql</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>keyword.other.cascade.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:\s*(drop)\s+(table)\s+(\w+)(\s+cascade)?\b)</string>
			<key>name</key>
			<string>meta.drop.sql</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.create.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.other.table.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:^\s*(alter)\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\s+)</string>
			<key>name</key>
			<string>meta.alter.sql</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>10</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>11</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>12</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>13</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>14</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>15</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
				<key>7</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>8</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.sql</string>
				</dict>
				<key>9</key>
				<dict>
					<key>name</key>
					<string>storage.type.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?xi)

				# normal stuff, capture 1
				 \b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\b

				# numeric suffix, capture 2 + 3i
				|\b(bit\svarying|character\s(?:varying)?|tinyint|var\schar|float|interval)\((\d+)\)

				# optional numeric suffix, capture 4 + 5i
				|\b(char|number|varchar\d?)\b(?:\((\d+)\))?

				# special case, capture 6 + 7i + 8i
				|\b(numeric|decimal)\b(?:\((\d+),(\d+)\))?

				# special case, captures 9, 10i, 11
				|\b(times?)\b(?:\((\d+)\))?(\swith(?:out)?\stime\szone\b)?

				# special case, captures 12, 13, 14i, 15
				|\b(timestamp)(?:(s|tz))?\b(?:\((\d+)\))?(\s(with|without)\stime\szone\b)?

			</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b((?:primary|foreign)\s+key|references|on\sdelete(\s+cascade)?|check|constraint)\b)</string>
			<key>name</key>
			<string>storage.modifier.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b\d+\b</string>
			<key>name</key>
			<string>constant.numeric.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(select(\s+distinct)?|insert\s+(ignore\s+)?into|update|delete|from|declare|set|where|group\sby|or|like|and|union(\s+all)?|having|order\sby|limit|(inner|cross)\s+join|join|straight_join|(left|right)(\s+outer)?\s+join|natural(\s+(left|right)(\s+outer)?)?\s+join)\b)</string>
			<key>name</key>
			<string>keyword.other.DML.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(on|((is\s+)?not\s+)?null)\b)</string>
			<key>name</key>
			<string>keyword.other.DDL.create.II.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(values|go|use|into|exec|execute|openquery)\b)</string>
			<key>name</key>
			<string>keyword.other.DML.II.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(begin(\s+work)?|start\s+transaction|commit(\s+work)?|rollback(\s+work)?)\b)</string>
			<key>name</key>
			<string>keyword.other.LUW.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(grant(\swith\sgrant\soption)?|revoke)\b)</string>
			<key>name</key>
			<string>keyword.other.authorization.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\bin\b)</string>
			<key>name</key>
			<string>keyword.other.data-integrity.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:^\s*(comment\s+on\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\s+.*?\s+(is)\s+)</string>
			<key>name</key>
			<string>keyword.other.object-comments.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i)\bAS\b</string>
			<key>name</key>
			<string>keyword.other.alias.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i)\b(DESC|ASC)\b</string>
			<key>name</key>
			<string>keyword.other.order.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\*</string>
			<key>name</key>
			<string>keyword.operator.star.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>[!&lt;&gt;]?=|&lt;&gt;|&lt;|&gt;</string>
			<key>name</key>
			<string>keyword.operator.comparison.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>-|\+|/</string>
			<key>name</key>
			<string>keyword.operator.math.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\|\|</string>
			<key>name</key>
			<string>keyword.operator.concatenator.sql</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>List of SQL99 built-in functions from http://www.oreilly.com/catalog/sqlnut/chapter/ch04.html</string>
			<key>match</key>
			<string>(?i)\b(CURRENT_(DATE|TIME(STAMP)?|USER)|(SESSION|SYSTEM)_USER)\b</string>
			<key>name</key>
			<string>support.function.scalar.sql</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>List of SQL99 built-in functions from http://www.oreilly.com/catalog/sqlnut/chapter/ch04.html</string>
			<key>match</key>
			<string>(?i)\b(AVG|COUNT|MIN|MAX|SUM)(?=\s*\()</string>
			<key>name</key>
			<string>support.function.aggregate.sql</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i)\b(CONCATENATE|CONVERT|LOWER|SUBSTRING|TRANSLATE|TRIM|UPPER)\b</string>
			<key>name</key>
			<string>support.function.string.sql</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.other.database-name.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>constant.other.table-name.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\w+?)\.(\w+)</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#strings</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#regexps</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.begin.sql</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.end.sql</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Allow for special ↩ behavior</string>
			<key>match</key>
			<string>(\()(\))</string>
			<key>name</key>
			<string>meta.block.sql</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>comments</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(^[ \t]+)?(?=--)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.whitespace.comment.leading.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?!\G)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>--</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.sql</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\n</string>
							<key>name</key>
							<string>comment.line.double-dash.sql</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(^[ \t]+)?(?=#)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.whitespace.comment.leading.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?!\G)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>#</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.sql</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\n</string>
							<key>name</key>
							<string>comment.line.number-sign.sql</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>/\*</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\*/</string>
					<key>name</key>
					<string>comment.block.c</string>
				</dict>
			</array>
		</dict>
		<key>regexps</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>/(?=\S.*/)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>/</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.regexp.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_interpolation</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\\/</string>
							<key>name</key>
							<string>constant.character.escape.slash.sql</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>%r\{</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>We should probably handle nested bracket pairs!?! -- Allan</string>
					<key>end</key>
					<string>\}</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.regexp.modr.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_interpolation</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>string_escape</key>
		<dict>
			<key>match</key>
			<string>\\.</string>
			<key>name</key>
			<string>constant.character.escape.sql</string>
		</dict>
		<key>string_interpolation</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.sql</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.sql</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(#\{)([^\}]*)(\})</string>
			<key>name</key>
			<string>string.interpolated.sql</string>
		</dict>
		<key>strings</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.</string>
					<key>match</key>
					<string>(')[^'\\]*(')</string>
					<key>name</key>
					<string>string.quoted.single.sql</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.single.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_escape</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.</string>
					<key>match</key>
					<string>(`)[^`\\]*(`)</string>
					<key>name</key>
					<string>string.quoted.other.backtick.sql</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>`</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>`</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.other.backtick.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_escape</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.</string>
					<key>match</key>
					<string>(")[^"#]*(")</string>
					<key>name</key>
					<string>string.quoted.double.sql</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>"</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>"</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.double.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_interpolation</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>%\{</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.sql</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.sql</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.other.quoted.brackets.sql</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string_interpolation</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.sql</string>
	<key>uuid</key>
	<string>C49120AC-6ECC-11D9-ACC8-000D93589AF6</string>
</dict>
</plist>
