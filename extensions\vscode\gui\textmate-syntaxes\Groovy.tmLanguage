<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>groovy</string>
		<string>gvy</string>
	</array>
	<key>foldingStartMarker</key>
	<string>(\{\s*$|^\s*// \{\{\{)</string>
	<key>foldingStopMarker</key>
	<string>^\s*(\}|// \}\}\}$)</string>
	<key>keyEquivalent</key>
	<string>^~G</string>
	<key>name</key>
	<string>Groovy</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.groovy</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(#!).+$\n</string>
			<key>name</key>
			<string>comment.line.hashbang.groovy</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.package.groovy</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>storage.modifier.package.groovy</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(package)\b(?:\s*([^ ;$]+)\s*(;)?)?</string>
			<key>name</key>
			<string>meta.package.groovy</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(import static)\b\s*</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.static.groovy</string>
				</dict>
			</dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.groovy</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>storage.modifier.import.groovy</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>storage.modifier.import.groovy</string>
			<key>end</key>
			<string>\s*(?:$|(?=%&gt;)(;))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.import.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\.</string>
					<key>name</key>
					<string>punctuation.separator.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\s</string>
					<key>name</key>
					<string>invalid.illegal.character_not_allowed_here.groovy</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(import)\b\s*</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.groovy</string>
				</dict>
			</dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.groovy</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>storage.modifier.import.groovy</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>storage.modifier.import.groovy</string>
			<key>end</key>
			<string>\s*(?:$|(?=%&gt;)|(;))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.import.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\.</string>
					<key>name</key>
					<string>punctuation.separator.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\s</string>
					<key>name</key>
					<string>invalid.illegal.character_not_allowed_here.groovy</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.groovy</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.other.import.static.groovy</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>storage.modifier.import.groovy</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.groovy</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(import)(?:\s+(static)\s+)\b(?:\s*([^ ;$]+)\s*(;)?)?</string>
			<key>name</key>
			<string>meta.import.groovy</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#groovy</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>annotations</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&lt;!\.)(@[^ (]+)(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.type.annotation.groovy</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.annotation-arguments.begin.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.annotation-arguments.end.groovy</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.declaration.annotation.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>constant.other.key.groovy</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>keyword.operator.assignment.groovy</string>
								</dict>
							</dict>
							<key>match</key>
							<string>(\w*)\s*(=)</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#values</string>
						</dict>
						<dict>
							<key>match</key>
							<string>,</string>
							<key>name</key>
							<string>punctuation.definition.seperator.groovy</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>(?&lt;!\.)@\S+</string>
					<key>name</key>
					<string>storage.type.annotation.groovy</string>
				</dict>
			</array>
		</dict>
		<key>anonymous-classes-and-new</key>
		<dict>
			<key>begin</key>
			<string>\bnew\b</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.control.new.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?&lt;=\)|\])(?!\s*{)|(?&lt;=})|(?=[;])|$</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(\w+)\s*(?=\[)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.type.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>}|(?=\s*(?:,|;|\)))|$</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\[</string>
							<key>end</key>
							<string>\]</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#groovy</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>{</string>
							<key>end</key>
							<string>(?=})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#groovy</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=\w.*\(?)</string>
					<key>end</key>
					<string>(?&lt;=\))|$</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>\(</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>storage.type.groovy</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#groovy</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>{</string>
					<key>end</key>
					<string>}</string>
					<key>name</key>
					<string>meta.inner-class.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#class-body</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>braces</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>end</key>
			<string>\}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#groovy-code</string>
				</dict>
			</array>
		</dict>
		<key>class</key>
		<dict>
			<key>begin</key>
			<string>(?=\w?[\w\s]*(?:class|(?:@)?interface|enum)\s+\w+)</string>
			<key>end</key>
			<string>}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.class.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.definition.class.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#storage-modifiers</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#comments</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.groovy</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.groovy</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(class|(?:@)?interface|enum)\s+(\w+)</string>
					<key>name</key>
					<string>meta.class.identifier.groovy</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>extends</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.extends.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?={|implements)</string>
					<key>name</key>
					<string>meta.definition.class.inherited.classes.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types-inherited</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#comments</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(implements)\s</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.implements.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\s*extends|\{)</string>
					<key>name</key>
					<string>meta.definition.class.implemented.interfaces.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types-inherited</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#comments</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>{</string>
					<key>end</key>
					<string>(?=})</string>
					<key>name</key>
					<string>meta.class.body.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#class-body</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>class-body</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#enum-values</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#constructors</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#groovy</string>
				</dict>
			</array>
		</dict>
		<key>closures</key>
		<dict>
			<key>begin</key>
			<string>\{(?=.*?-&gt;)</string>
			<key>end</key>
			<string>\}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&lt;=\{)(?=[^\}]*?-&gt;)</string>
					<key>end</key>
					<string>-&gt;</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.groovy</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(?!-&gt;)</string>
							<key>end</key>
							<string>(?=-&gt;)</string>
							<key>name</key>
							<string>meta.closure.parameters.groovy</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>(?!,|-&gt;)</string>
									<key>end</key>
									<string>(?=,|-&gt;)</string>
									<key>name</key>
									<string>meta.closure.parameter.groovy</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>begin</key>
											<string>=</string>
											<key>beginCaptures</key>
											<dict>
												<key>0</key>
												<dict>
													<key>name</key>
													<string>keyword.operator.assignment.groovy</string>
												</dict>
											</dict>
											<key>end</key>
											<string>(?=,|-&gt;)</string>
											<key>name</key>
											<string>meta.parameter.default.groovy</string>
											<key>patterns</key>
											<array>
												<dict>
													<key>include</key>
													<string>#groovy-code</string>
												</dict>
											</array>
										</dict>
										<dict>
											<key>include</key>
											<string>#parameters</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=[^}])</string>
					<key>end</key>
					<string>(?=\})</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#groovy-code</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>comment-block</key>
		<dict>
			<key>begin</key>
			<string>/\*</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\*/</string>
			<key>name</key>
			<string>comment.block.groovy</string>
		</dict>
		<key>comments</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.groovy</string>
						</dict>
					</dict>
					<key>match</key>
					<string>/\*\*/</string>
					<key>name</key>
					<string>comment.block.empty.groovy</string>
				</dict>
				<dict>
					<key>include</key>
					<string>text.html.javadoc</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#comment-block</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.groovy</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(//).*$\n?</string>
					<key>name</key>
					<string>comment.line.double-slash.groovy</string>
				</dict>
			</array>
		</dict>
		<key>constants</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b([A-Z][A-Z0-9_]+)\b</string>
					<key>name</key>
					<string>constant.other.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(true|false|null)\b</string>
					<key>name</key>
					<string>constant.language.groovy</string>
				</dict>
			</array>
		</dict>
		<key>constructors</key>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>begin</key>
			<string>(?&lt;=;|^)(?=\s*(?:(?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)\s+)*[A-Z]\w*\()</string>
			<key>end</key>
			<string>}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#method-content</string>
				</dict>
			</array>
		</dict>
		<key>enum-values</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&lt;=;|^)\s*\b([A-Z0-9_]+)(?=\s*(?:,|;|}|\(|$))</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>constant.enum.name.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>,|;|(?=})|^(?!\s*\w+\s*(?:,|$))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\(</string>
							<key>end</key>
							<string>\)</string>
							<key>name</key>
							<string>meta.enum.value.groovy</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>,</string>
									<key>name</key>
									<string>punctuation.definition.seperator.parameter.groovy</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#groovy-code</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>groovy</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#comments</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#class</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variables</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#methods</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#annotations</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#groovy-code</string>
				</dict>
			</array>
		</dict>
		<key>groovy-code</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#groovy-code-minus-map-keys</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#map-keys</string>
				</dict>
			</array>
		</dict>
		<key>groovy-code-minus-map-keys</key>
		<dict>
			<key>comment</key>
			<string>In some situations, maps can't be declared without enclosing []'s,
				therefore we create a collection of everything but that</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#comments</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#annotations</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#support-functions</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#keyword-language</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#values</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#anonymous-classes-and-new</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#keyword-operator</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#types</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#storage-modifiers</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#parens</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#closures</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#braces</string>
				</dict>
			</array>
		</dict>
		<key>keyword</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#keyword-operator</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#keyword-language</string>
				</dict>
			</array>
		</dict>
		<key>keyword-language</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(try|catch|finally|throw)\b</string>
					<key>name</key>
					<string>keyword.control.exception.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b((?&lt;!\.)(?:return|break|continue|default|do|while|for|switch|if|else))\b</string>
					<key>name</key>
					<string>keyword.control.groovy</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\bcase\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>keyword.control.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>:</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.case-terminator.groovy</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.case.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#groovy-code-minus-map-keys</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(assert)\s</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.assert.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>$|;|}</string>
					<key>name</key>
					<string>meta.declaration.assertion.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>:</string>
							<key>name</key>
							<string>keyword.operator.assert.expression-seperator.groovy</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#groovy-code-minus-map-keys</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(throws)\b</string>
					<key>name</key>
					<string>keyword.other.throws.groovy</string>
				</dict>
			</array>
		</dict>
		<key>keyword-operator</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(as)\b</string>
					<key>name</key>
					<string>keyword.operator.as.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(in)\b</string>
					<key>name</key>
					<string>keyword.operator.in.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\?\:</string>
					<key>name</key>
					<string>keyword.operator.elvis.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\*\:</string>
					<key>name</key>
					<string>keyword.operator.spreadmap.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\.\.</string>
					<key>name</key>
					<string>keyword.operator.range.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\-&gt;</string>
					<key>name</key>
					<string>keyword.operator.arrow.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>&lt;&lt;</string>
					<key>name</key>
					<string>keyword.operator.leftshift.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(?&lt;=\S)\.(?=\S)</string>
					<key>name</key>
					<string>keyword.operator.navigation.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(?&lt;=\S)\?\.(?=\S)</string>
					<key>name</key>
					<string>keyword.operator.safe-navigation.groovy</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\?</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.ternary.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=$|\)|}|])</string>
					<key>name</key>
					<string>meta.evaluation.ternary.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>:</string>
							<key>name</key>
							<string>keyword.operator.ternary.expression-seperator.groovy</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#groovy-code-minus-map-keys</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>==~</string>
					<key>name</key>
					<string>keyword.operator.match.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>=~</string>
					<key>name</key>
					<string>keyword.operator.find.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(instanceof)\b</string>
					<key>name</key>
					<string>keyword.operator.instanceof.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(===|==|!=|&lt;=|&gt;=|&lt;=&gt;|&lt;&gt;|&lt;|&gt;|&lt;&lt;)</string>
					<key>name</key>
					<string>keyword.operator.comparison.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>=</string>
					<key>name</key>
					<string>keyword.operator.assignment.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\-\-|\+\+)</string>
					<key>name</key>
					<string>keyword.operator.increment-decrement.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\-|\+|\*|\/|%)</string>
					<key>name</key>
					<string>keyword.operator.arithmetic.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(!|&amp;&amp;|\|\|)</string>
					<key>name</key>
					<string>keyword.operator.logical.groovy</string>
				</dict>
			</array>
		</dict>
		<key>language-variables</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(this|super)\b</string>
					<key>name</key>
					<string>variable.language.groovy</string>
				</dict>
			</array>
		</dict>
		<key>map-keys</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>constant.other.key.groovy</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.seperator.key-value.groovy</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\w+)\s*(:)</string>
				</dict>
			</array>
		</dict>
		<key>method-call</key>
		<dict>
			<key>begin</key>
			<string>([\w$]+)(\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.method.groovy</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.method-parameters.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.method-parameters.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.method-call.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>,</string>
					<key>name</key>
					<string>punctuation.definition.seperator.parameter.groovy</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#groovy-code</string>
				</dict>
			</array>
		</dict>
		<key>method-content</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\s</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#annotations</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=(?:\w|&lt;)[^\(]*\s+(?:[\w$]|&lt;)+\s*\()</string>
					<key>end</key>
					<string>(?=[\w$]+\s*\()</string>
					<key>name</key>
					<string>meta.method.return-type.java</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#storage-modifiers</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>([\w$]+)\s*\(</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.java</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>meta.definition.method.signature.java</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(?=[^)])</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>name</key>
							<string>meta.method.parameters.groovy</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>(?=[^,)])</string>
									<key>end</key>
									<string>(?=,|\))</string>
									<key>name</key>
									<string>meta.method.parameter.groovy</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>match</key>
											<string>,</string>
											<key>name</key>
											<string>punctuation.definition.separator.groovy</string>
										</dict>
										<dict>
											<key>begin</key>
											<string>=</string>
											<key>beginCaptures</key>
											<dict>
												<key>0</key>
												<dict>
													<key>name</key>
													<string>keyword.operator.assignment.groovy</string>
												</dict>
											</dict>
											<key>end</key>
											<string>(?=,|\))</string>
											<key>name</key>
											<string>meta.parameter.default.groovy</string>
											<key>patterns</key>
											<array>
												<dict>
													<key>include</key>
													<string>#groovy-code</string>
												</dict>
											</array>
										</dict>
										<dict>
											<key>include</key>
											<string>#parameters</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=&lt;)</string>
					<key>end</key>
					<string>(?=\s)</string>
					<key>name</key>
					<string>meta.method.paramerised-type.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>&lt;</string>
							<key>end</key>
							<string>&gt;</string>
							<key>name</key>
							<string>storage.type.parameters.groovy</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#types</string>
								</dict>
								<dict>
									<key>match</key>
									<string>,</string>
									<key>name</key>
									<string>punctuation.definition.seperator.groovy</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>throws</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?={|;)|^(?=\s*(?:[^{\s]|$))</string>
					<key>name</key>
					<string>meta.throwables.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>{</string>
					<key>end</key>
					<string>(?=})</string>
					<key>name</key>
					<string>meta.method.body.java</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#groovy-code</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>methods</key>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>begin</key>
			<string>(?x:(?&lt;=;|^|{)(?=\s*
                (?:
                    (?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final) # visibility/modifier
                        |
                    (?:def)
                        |
                    (?:
                        (?:
                            (?:void|boolean|byte|char|short|int|float|long|double)
                                |
                            (?:@?(?:[a-zA-Z]\w*\.)*[A-Z]+\w*) # object type
                        )
                        [\[\]]*
                        (?:&lt;.*&gt;)?
                    )

                )
                \s+
                ([^=]+\s+)?\w+\s*\(
			))</string>
			<key>end</key>
			<string>}|(?=[^{])</string>
			<key>name</key>
			<string>meta.definition.method.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#method-content</string>
				</dict>
			</array>
		</dict>
		<key>nest_curly</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nest_curly</string>
				</dict>
			</array>
		</dict>
		<key>numbers</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>((0(x|X)[0-9a-fA-F]*)|(\+|-)?\b(([0-9]+\.?[0-9]*)|(\.[0-9]+))((e|E)(\+|-)?[0-9]+)?)([LlFfUuDdg]|UL|ul)?\b</string>
					<key>name</key>
					<string>constant.numeric.groovy</string>
				</dict>
			</array>
		</dict>
		<key>object-types</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b((?:[a-z]\w*\.)*(?:[A-Z]+\w*[a-z]+\w*|UR[LI]))&lt;</string>
					<key>end</key>
					<string>&gt;|[^\w\s,\?&lt;\[\]]</string>
					<key>name</key>
					<string>storage.type.generic.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>&lt;</string>
							<key>comment</key>
							<string>This is just to support &lt;&gt;'s with no actual type prefix</string>
							<key>end</key>
							<string>&gt;|[^\w\s,\[\]&lt;]</string>
							<key>name</key>
							<string>storage.type.generic.groovy</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b((?:[a-z]\w*\.)*[A-Z]+\w*[a-z]+\w*)(?=\[)</string>
					<key>end</key>
					<string>(?=[^\]\s])</string>
					<key>name</key>
					<string>storage.type.object.array.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\[</string>
							<key>end</key>
							<string>\]</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#groovy</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(?:[a-zA-Z]\w*\.)*(?:[A-Z]+\w*[a-z]+\w*|UR[LI])\b</string>
					<key>name</key>
					<string>storage.type.groovy</string>
				</dict>
			</array>
		</dict>
		<key>object-types-inherited</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b((?:[a-zA-Z]\w*\.)*[A-Z]+\w*[a-z]+\w*)&lt;</string>
					<key>end</key>
					<string>&gt;|[^\w\s,\?&lt;\[\]]</string>
					<key>name</key>
					<string>entity.other.inherited-class.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#object-types-inherited</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>&lt;</string>
							<key>comment</key>
							<string>This is just to support &lt;&gt;'s with no actual type prefix</string>
							<key>end</key>
							<string>&gt;|[^\w\s,\[\]&lt;]</string>
							<key>name</key>
							<string>storage.type.generic.groovy</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.dereference.groovy</string>
						</dict>
					</dict>
					<key>match</key>
					<string>\b(?:[a-zA-Z]\w*(\.))*[A-Z]+\w*[a-z]+\w*\b</string>
					<key>name</key>
					<string>entity.other.inherited-class.groovy</string>
				</dict>
			</array>
		</dict>
		<key>parameters</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#annotations</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#storage-modifiers</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#types</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\w+</string>
					<key>name</key>
					<string>variable.parameter.method.groovy</string>
				</dict>
			</array>
		</dict>
		<key>parens</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>end</key>
			<string>\)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#groovy-code</string>
				</dict>
			</array>
		</dict>
		<key>primitive-arrays</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(?:void|boolean|byte|char|short|int|float|long|double)(\[\])*\b</string>
					<key>name</key>
					<string>storage.type.primitive.array.groovy</string>
				</dict>
			</array>
		</dict>
		<key>primitive-types</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(?:void|boolean|byte|char|short|int|float|long|double)\b</string>
					<key>name</key>
					<string>storage.type.primitive.groovy</string>
				</dict>
			</array>
		</dict>
		<key>regexp</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>/(?=[^/]+/([^&gt;]|$))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.regexp.begin.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>/</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.regexp.end.groovy</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.regexp.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\\.</string>
							<key>name</key>
							<string>constant.character.escape.groovy</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>~"</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.regexp.begin.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>"</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.regexp.end.groovy</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.regexp.compiled.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\\.</string>
							<key>name</key>
							<string>constant.character.escape.groovy</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>storage-modifiers</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(private|protected|public)\b</string>
					<key>name</key>
					<string>storage.modifier.access-control.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(static)\b</string>
					<key>name</key>
					<string>storage.modifier.static.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(final)\b</string>
					<key>name</key>
					<string>storage.modifier.final.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(native|synchronized|abstract|threadsafe|transient)\b</string>
					<key>name</key>
					<string>storage.modifier.other.groovy</string>
				</dict>
			</array>
		</dict>
		<key>string-quoted-double</key>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-quoted-double-contents</string>
				</dict>
			</array>
		</dict>
		<key>string-quoted-double-contents</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.groovy</string>
				</dict>
				<dict>
					<key>applyEndPatternLast</key>
					<integer>1</integer>
					<key>begin</key>
					<string>\$\w</string>
					<key>end</key>
					<string>(?=\W)</string>
					<key>name</key>
					<string>variable.other.interpolated.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\w</string>
							<key>name</key>
							<string>variable.other.interpolated.groovy</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\.</string>
							<key>name</key>
							<string>keyword.other.dereference.groovy</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\$\{</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.embedded.groovy</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>name</key>
					<string>source.groovy.embedded.source</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#nest_curly</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>string-quoted-double-multiline</key>
		<dict>
			<key>begin</key>
			<string>"""</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"""</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.multiline.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-quoted-double-contents</string>
				</dict>
			</array>
		</dict>
		<key>string-quoted-single</key>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-quoted-single-contents</string>
				</dict>
			</array>
		</dict>
		<key>string-quoted-single-contents</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.groovy</string>
				</dict>
			</array>
		</dict>
		<key>string-quoted-single-multiline</key>
		<dict>
			<key>begin</key>
			<string>'''</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'''</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.multiline.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-quoted-single-contents</string>
				</dict>
			</array>
		</dict>
		<key>strings</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-quoted-double-multiline</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-quoted-single-multiline</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-quoted-double</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-quoted-single</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#regexp</string>
				</dict>
			</array>
		</dict>
		<key>structures</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.structure.begin.groovy</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.structure.end.groovy</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.groovy</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#groovy-code</string>
				</dict>
				<dict>
					<key>match</key>
					<string>,</string>
					<key>name</key>
					<string>punctuation.definition.separator.groovy</string>
				</dict>
			</array>
		</dict>
		<key>support-functions</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>(?x)\b(?:sprintf|print(?:f|ln)?)\b</string>
					<key>name</key>
					<string>support.function.print.groovy</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(?x)\b(?:shouldFail|fail(?:NotEquals)?|ass(?:ume|ert(?:S(?:cript|ame)|N(?:ot(?:Same|
					Null)|ull)|Contains|T(?:hat|oString|rue)|Inspect|Equals|False|Length|
					ArrayEquals)))\b</string>
					<key>name</key>
					<string>support.function.testing.groovy</string>
				</dict>
			</array>
		</dict>
		<key>types</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(def)\b</string>
					<key>name</key>
					<string>storage.type.def.groovy</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#primitive-types</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#primitive-arrays</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#object-types</string>
				</dict>
			</array>
		</dict>
		<key>values</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#language-variables</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#strings</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#numbers</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#constants</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#types</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#structures</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#method-call</string>
				</dict>
			</array>
		</dict>
		<key>variables</key>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?x:(?=
                        (?:
                            (?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final) # visibility/modifier
                                |
                            (?:def)
                                |
                            (?:void|boolean|byte|char|short|int|float|long|double)
                                |
                            (?:(?:[a-z]\w*\.)*[A-Z]+\w*) # object type
                        )
                        \s+
                        [\w\d_&lt;&gt;\[\],\s]+
                        (?:=|$)

        			))</string>
					<key>end</key>
					<string>;|$</string>
					<key>name</key>
					<string>meta.definition.variable.groovy</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\s</string>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>constant.variable.groovy</string>
								</dict>
							</dict>
							<key>match</key>
							<string>([A-Z_0-9]+)\s+(?=\=)</string>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>meta.definition.variable.name.groovy</string>
								</dict>
							</dict>
							<key>match</key>
							<string>(\w[^\s,]*)\s+(?=\=)</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>=</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>keyword.operator.assignment.groovy</string>
								</dict>
							</dict>
							<key>end</key>
							<string>$</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#groovy-code</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>meta.definition.variable.name.groovy</string>
								</dict>
							</dict>
							<key>match</key>
							<string>(\w[^\s=]*)(?=\s*($|;))</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#groovy-code</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.groovy</string>
	<key>uuid</key>
	<string>B3A64888-EBBB-4436-8D9E-F1169C5D7613</string>
</dict>
</plist>