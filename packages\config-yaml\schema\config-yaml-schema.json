{"$ref": "#/definitions/ConfigYaml", "definitions": {"ConfigYaml": {"type": "object", "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "schema": {"type": "string"}, "metadata": {"allOf": [{"type": "object", "additionalProperties": {"type": "string"}}, {"type": "object", "properties": {"tags": {"type": "string"}, "sourceCodeUrl": {"type": "string"}, "description": {"type": "string"}, "author": {"type": "string"}, "license": {"type": "string"}, "iconUrl": {"type": "string"}}}]}, "models": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "model": {"type": "string"}, "apiKey": {"type": "string"}, "apiBase": {"type": "string"}, "maxStopWords": {"type": "number"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["chat", "autocomplete", "embed", "rerank", "edit", "apply", "summarize"]}}, "capabilities": {"type": "array", "items": {"type": "string", "enum": ["tool_use", "image_input"]}}, "defaultCompletionOptions": {"type": "object", "properties": {"contextLength": {"type": "number"}, "maxTokens": {"type": "number"}, "temperature": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}, "n": {"type": "number"}, "reasoning": {"type": "boolean"}, "reasoningBudgetTokens": {"type": "number"}, "promptCaching": {"type": "boolean"}, "stream": {"type": "boolean"}}, "additionalProperties": false}, "cacheBehavior": {"type": "object", "properties": {"cacheSystemMessage": {"type": "boolean"}, "cacheConversation": {"type": "boolean"}}, "additionalProperties": false}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "embedOptions": {"type": "object", "properties": {"maxChunkSize": {"type": "number"}, "maxBatchSize": {"type": "number"}, "embeddingPrefixes": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "chatOptions": {"type": "object", "properties": {"baseSystemMessage": {"type": "string"}, "baseAgentSystemMessage": {"type": "string"}}, "additionalProperties": false}, "promptTemplates": {"type": "object", "properties": {"apply": {"type": "string"}, "chat": {"type": "string", "enum": ["llama2", "alpaca", "zephyr", "phi2", "phind", "anthropic", "chatml", "none", "openchat", "deepseek", "xwin-coder", "neural-chat", "codellama-70b", "llava", "gemma", "granite", "llama3", "codestral"]}, "edit": {"type": "string"}, "autocomplete": {"type": "string"}}, "additionalProperties": false}, "useLegacyCompletionsEndpoint": {"type": "boolean"}, "env": {"type": "object", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "autocompleteOptions": {"type": "object", "properties": {"disable": {"type": "boolean"}, "maxPromptTokens": {"type": "number"}, "debounceDelay": {"type": "number"}, "modelTimeout": {"type": "number"}, "maxSuffixPercentage": {"type": "number"}, "prefixPercentage": {"type": "number"}, "template": {"type": "string"}, "onlyMyCode": {"type": "boolean"}}, "additionalProperties": false}, "provider": {"type": "string", "const": "continue-proxy"}, "apiKeyLocation": {"type": "string"}, "orgScopeId": {"type": ["string", "null"]}, "onPremProxyUrl": {"type": ["string", "null"]}}, "required": ["name", "model", "provider", "apiKeyLocation", "orgScopeId", "onPremProxyUrl"], "additionalProperties": false}, {"type": "object", "properties": {"name": {"type": "string"}, "model": {"type": "string"}, "apiKey": {"type": "string"}, "apiBase": {"type": "string"}, "maxStopWords": {"type": "number"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["chat", "autocomplete", "embed", "rerank", "edit", "apply", "summarize"]}}, "capabilities": {"type": "array", "items": {"type": "string", "enum": ["tool_use", "image_input"]}}, "defaultCompletionOptions": {"type": "object", "properties": {"contextLength": {"type": "number"}, "maxTokens": {"type": "number"}, "temperature": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}, "n": {"type": "number"}, "reasoning": {"type": "boolean"}, "reasoningBudgetTokens": {"type": "number"}, "promptCaching": {"type": "boolean"}, "stream": {"type": "boolean"}}, "additionalProperties": false}, "cacheBehavior": {"type": "object", "properties": {"cacheSystemMessage": {"type": "boolean"}, "cacheConversation": {"type": "boolean"}}, "additionalProperties": false}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "embedOptions": {"type": "object", "properties": {"maxChunkSize": {"type": "number"}, "maxBatchSize": {"type": "number"}, "embeddingPrefixes": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "chatOptions": {"type": "object", "properties": {"baseSystemMessage": {"type": "string"}, "baseAgentSystemMessage": {"type": "string"}}, "additionalProperties": false}, "promptTemplates": {"type": "object", "properties": {"apply": {"type": "string"}, "chat": {"type": "string", "enum": ["llama2", "alpaca", "zephyr", "phi2", "phind", "anthropic", "chatml", "none", "openchat", "deepseek", "xwin-coder", "neural-chat", "codellama-70b", "llava", "gemma", "granite", "llama3", "codestral"]}, "edit": {"type": "string"}, "autocomplete": {"type": "string"}}, "additionalProperties": false}, "useLegacyCompletionsEndpoint": {"type": "boolean"}, "env": {"type": "object", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "autocompleteOptions": {"type": "object", "properties": {"disable": {"type": "boolean"}, "maxPromptTokens": {"type": "number"}, "debounceDelay": {"type": "number"}, "modelTimeout": {"type": "number"}, "maxSuffixPercentage": {"type": "number"}, "prefixPercentage": {"type": "number"}, "template": {"type": "string"}, "onlyMyCode": {"type": "boolean"}}, "additionalProperties": false}, "provider": {"type": "string"}}, "required": ["name", "model", "provider"], "additionalProperties": false}]}, {"type": "object", "properties": {"uses": {"anyOf": [{"type": "string"}, {"type": "string", "enum": ["anthropic/claude-3-7-sonnet", "togetherai/llama-4-maverick-instruct-17bx128e", "google/gemini-2.5-pro", "mistral/codestral", "voyageai/voyage-code-3", "relace/instant-apply", "xai/grok-2", "openai/gpt-4o", "togetherai/llama-4-scout-instruct-17bx16e", "anthropic/claude-3-5-sonnet", "google/gemini-2.0-flash", "voyageai/rerank-2", "ollama/deepseek-r1", "morphllm/morph-v0", "anthropic/claude-3-5-haiku", "lmstudio/deepseek-r1", "openai/o3-mini", "voyageai/voyage-code-2", "ollama/qwen2.5-coder-1.5b", "openai/gpt-4o-mini", "openai/o1", "mistral/mistral-embed", "ollama/nomic-embed-text-latest", "lmstudio/deepseek-r1-8b", "mistral/mistral-large", "lmstudio/qwen2.5-coder-1.5b", "ollama/deepseek-r1-8b", "openai/text-embedding-3-large"]}]}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "model": {"type": "string"}, "apiKey": {"type": "string"}, "apiBase": {"type": "string"}, "maxStopWords": {"type": "number"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["chat", "autocomplete", "embed", "rerank", "edit", "apply", "summarize"]}}, "capabilities": {"type": "array", "items": {"type": "string", "enum": ["tool_use", "image_input"]}}, "defaultCompletionOptions": {"type": "object", "properties": {"contextLength": {"type": "number"}, "maxTokens": {"type": "number"}, "temperature": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}, "n": {"type": "number"}, "reasoning": {"type": "boolean"}, "reasoningBudgetTokens": {"type": "number"}, "promptCaching": {"type": "boolean"}, "stream": {"type": "boolean"}}, "additionalProperties": false}, "cacheBehavior": {"type": "object", "properties": {"cacheSystemMessage": {"type": "boolean"}, "cacheConversation": {"type": "boolean"}}, "additionalProperties": false}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "embedOptions": {"type": "object", "properties": {"maxChunkSize": {"type": "number"}, "maxBatchSize": {"type": "number"}, "embeddingPrefixes": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "chatOptions": {"type": "object", "properties": {"baseSystemMessage": {"type": "string"}, "baseAgentSystemMessage": {"type": "string"}}, "additionalProperties": false}, "promptTemplates": {"type": "object", "properties": {"apply": {"type": "string"}, "chat": {"type": "string", "enum": ["llama2", "alpaca", "zephyr", "phi2", "phind", "anthropic", "chatml", "none", "openchat", "deepseek", "xwin-coder", "neural-chat", "codellama-70b", "llava", "gemma", "granite", "llama3", "codestral"]}, "edit": {"type": "string"}, "autocomplete": {"type": "string"}}, "additionalProperties": false}, "useLegacyCompletionsEndpoint": {"type": "boolean"}, "env": {"type": "object", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "autocompleteOptions": {"type": "object", "properties": {"disable": {"type": "boolean"}, "maxPromptTokens": {"type": "number"}, "debounceDelay": {"type": "number"}, "modelTimeout": {"type": "number"}, "maxSuffixPercentage": {"type": "number"}, "prefixPercentage": {"type": "number"}, "template": {"type": "string"}, "onlyMyCode": {"type": "boolean"}}, "additionalProperties": false}, "provider": {"type": "string", "const": "continue-proxy"}, "apiKeyLocation": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "properties": {"name": {"type": "string"}, "model": {"type": "string"}, "apiKey": {"type": "string"}, "apiBase": {"type": "string"}, "maxStopWords": {"type": "number"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["chat", "autocomplete", "embed", "rerank", "edit", "apply", "summarize"]}}, "capabilities": {"type": "array", "items": {"type": "string", "enum": ["tool_use", "image_input"]}}, "defaultCompletionOptions": {"type": "object", "properties": {"contextLength": {"type": "number"}, "maxTokens": {"type": "number"}, "temperature": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}, "n": {"type": "number"}, "reasoning": {"type": "boolean"}, "reasoningBudgetTokens": {"type": "number"}, "promptCaching": {"type": "boolean"}, "stream": {"type": "boolean"}}, "additionalProperties": false}, "cacheBehavior": {"type": "object", "properties": {"cacheSystemMessage": {"type": "boolean"}, "cacheConversation": {"type": "boolean"}}, "additionalProperties": false}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "embedOptions": {"type": "object", "properties": {"maxChunkSize": {"type": "number"}, "maxBatchSize": {"type": "number"}, "embeddingPrefixes": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "chatOptions": {"type": "object", "properties": {"baseSystemMessage": {"type": "string"}, "baseAgentSystemMessage": {"type": "string"}}, "additionalProperties": false}, "promptTemplates": {"type": "object", "properties": {"apply": {"type": "string"}, "chat": {"type": "string", "enum": ["llama2", "alpaca", "zephyr", "phi2", "phind", "anthropic", "chatml", "none", "openchat", "deepseek", "xwin-coder", "neural-chat", "codellama-70b", "llava", "gemma", "granite", "llama3", "codestral"]}, "edit": {"type": "string"}, "autocomplete": {"type": "string"}}, "additionalProperties": false}, "useLegacyCompletionsEndpoint": {"type": "boolean"}, "env": {"type": "object", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "autocompleteOptions": {"type": "object", "properties": {"disable": {"type": "boolean"}, "maxPromptTokens": {"type": "number"}, "debounceDelay": {"type": "number"}, "modelTimeout": {"type": "number"}, "maxSuffixPercentage": {"type": "number"}, "prefixPercentage": {"type": "number"}, "template": {"type": "string"}, "onlyMyCode": {"type": "boolean"}}, "additionalProperties": false}, "provider": {"type": "string"}}, "additionalProperties": false}]}}, "required": ["uses"], "additionalProperties": false}]}}, "context": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "provider": {"type": "string"}, "params": {}}, "required": ["provider"], "additionalProperties": false}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"type": "object", "properties": {"name": {"type": "string"}, "provider": {"type": "string"}, "params": {}}, "additionalProperties": false}}, "required": ["uses"], "additionalProperties": false}]}}, "data": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "destination": {"type": "string"}, "schema": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9A-Za-z-][0-9A-Za-z-]*)(?:\\.(?:0|[1-9A-Za-z-][0-9A-Za-z-]*))*))?(?:\\+([0-9A-Za-z-]+(?:\\.[0-9A-Za-z-]+)*))?$"}, "level": {"type": "string", "enum": ["all", "noCode"]}, "events": {"type": "array", "items": {"type": "string"}}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "apiKey": {"type": "string"}}, "required": ["name", "destination", "schema"], "additionalProperties": false}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"type": "object", "properties": {"name": {"type": "string"}, "destination": {"type": "string"}, "schema": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9A-Za-z-][0-9A-Za-z-]*)(?:\\.(?:0|[1-9A-Za-z-][0-9A-Za-z-]*))*))?(?:\\+([0-9A-Za-z-]+(?:\\.[0-9A-Za-z-]+)*))?$"}, "level": {"type": "string", "enum": ["all", "noCode"]}, "events": {"type": "array", "items": {"type": "string"}}, "requestOptions": {"type": "object", "properties": {"timeout": {"type": "number"}, "verifySsl": {"type": "boolean"}, "caBundlePath": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "proxy": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "extraBodyProperties": {"type": "object", "additionalProperties": {}}, "noProxy": {"type": "array", "items": {"type": "string"}}, "clientCertificate": {"type": "object", "properties": {"cert": {"type": "string"}, "key": {"type": "string"}, "passphrase": {"type": "string"}}, "required": ["cert", "key"], "additionalProperties": false}}, "additionalProperties": false}, "apiKey": {"type": "string"}}, "additionalProperties": false}}, "required": ["uses"], "additionalProperties": false}]}}, "mcpServers": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "command": {"type": "string"}, "type": {"type": "string", "enum": ["sse", "stdio", "streamable-http"]}, "url": {"type": "string"}, "faviconUrl": {"type": "string"}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"type": "object", "additionalProperties": {"type": "string"}}, "connectionTimeout": {"type": "number", "exclusiveMinimum": 0}}, "required": ["name"], "additionalProperties": false}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"type": "object", "properties": {"name": {"type": "string"}, "command": {"type": "string"}, "type": {"type": "string", "enum": ["sse", "stdio", "streamable-http"]}, "url": {"type": "string"}, "faviconUrl": {"type": "string"}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"type": "object", "additionalProperties": {"type": "string"}}, "connectionTimeout": {"type": "number", "exclusiveMinimum": 0}}, "additionalProperties": false}}, "required": ["uses"], "additionalProperties": false}]}}, "rules": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "string"}, {"type": "object", "properties": {"name": {"type": "string"}, "rule": {"type": "string"}, "description": {"type": "string"}, "globs": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "alwaysApply": {"type": "boolean"}}, "required": ["name", "rule"], "additionalProperties": false}]}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["uses"], "additionalProperties": false}]}}, "prompts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "prompt": {"type": "string"}}, "required": ["name", "prompt"], "additionalProperties": false}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "prompt": {"type": "string"}}, "additionalProperties": false}}, "required": ["uses"], "additionalProperties": false}]}}, "docs": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string"}, "startUrl": {"type": "string"}, "rootUrl": {"type": "string"}, "faviconUrl": {"type": "string"}}, "required": ["name", "startUrl"], "additionalProperties": false}, {"type": "object", "properties": {"uses": {"type": "string"}, "with": {"type": "object", "additionalProperties": {"type": "string"}}, "override": {"type": "object", "properties": {"name": {"type": "string"}, "startUrl": {"type": "string"}, "rootUrl": {"type": "string"}, "faviconUrl": {"type": "string"}}, "additionalProperties": false}}, "required": ["uses"], "additionalProperties": false}]}}}, "required": ["name", "version"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}