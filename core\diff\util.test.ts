// Generated by continue

import { matchLine, streamLines, generateLines } from "./util";
import { ChatMessage } from "../index";
import { jest } from "@jest/globals";

describe.skip("matchLine", () => {
  it("should match empty lines if the first old line is also empty", () => {
    const newLine = "   ";
    const oldLines = ["    ", "some code"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: 0,
      isPerfectMatch: true,
      newLine: "",
    });
  });

  it("should return matchIndex -1 if newLine is an end bracket and i > 4", () => {
    const newLine = "}";
    const oldLines = ["line 1", "line 2", "line 3", "line 4", "line 5", "}"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: -1,
      isPerfectMatch: false,
      newLine: "}",
    });
  });

  it("should match end bracket if within first 5 lines", () => {
    const newLine = "}";
    const oldLines = ["line 1", "line 2", "line 3", "}", "line 5", "}"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: 3,
      isPerfectMatch: true,
      newLine: "}",
    });
  });

  it("should match lines perfectly", () => {
    const newLine = "const a = 5;";
    const oldLines = ["let b = 6;", "const a = 5;", "console.log(a);"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: 1,
      isPerfectMatch: true,
      newLine: "const a = 5;",
    });
  });

  it("should match lines with tolerable differences", () => {
    const newLine = "console.log(a);";
    const oldLines = ["const a = 5;", "console.log(b);", "console.log( a );"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: 2,
      isPerfectMatch: false,
      newLine: "console.log(a);",
    });
  });

  it("should return matchIndex -1 if no match found", () => {
    const newLine = "new line that does not exist";
    const oldLines = ["line 1", "line 2", "line 3"];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: -1,
      isPerfectMatch: false,
      newLine: "new line that does not exist",
    });
  });

  it("should fix indentation if permissible and line length > 8", () => {
    const newLine = "    const a = 5;";
    const oldLines = ["const a = 5;"];
    const result = matchLine(newLine, oldLines, false);
    expect(result).toEqual({
      matchIndex: 0,
      isPerfectMatch: true,
      newLine: "const a = 5;",
    });
  });

  it("should not fix indentation if not permissible and line length is short", () => {
    const newLine = "  for";
    const oldLines = ["for"];
    const result = matchLine(newLine, oldLines, false);
    expect(result).toEqual({
      matchIndex: 0,
      isPerfectMatch: false,
      newLine: "  for",
    });
  });

  it("should fix indentation even if line length <=8 when permissiveAboutIndentation is true", () => {
    const newLine = "  for";
    const oldLines = ["for"];
    const result = matchLine(newLine, oldLines, true);
    expect(result).toEqual({
      matchIndex: 0,
      isPerfectMatch: true,
      newLine: "for",
    });
  });

  it("should match lines that are empty strings", () => {
    const newLine = "";
    const oldLines = [""];
    const result = matchLine(newLine, oldLines);
    expect(result).toEqual({
      matchIndex: 0,
      isPerfectMatch: true,
      newLine: "",
    });
  });
});

describe("streamLines", () => {
  it("should split chunks into lines correctly", async () => {
    async function* streamCompletion(): AsyncGenerator<string> {
      yield "line1\nline";
      yield "2\nline3\n";
      yield "line4";
    }

    const resultLines: string[] = [];
    for await (const line of streamLines(streamCompletion())) {
      resultLines.push(line);
    }

    expect(resultLines).toEqual(["line1", "line2", "line3", "line4"]);
  });

  it("should handle ChatMessage chunks", async () => {
    const messageChunk1: ChatMessage = {
      role: "assistant",
      content: "line1\nline",
    };
    const messageChunk2: ChatMessage = {
      role: "assistant",
      content: "2\nline3\n",
    };
    const messageChunk3: ChatMessage = {
      role: "assistant",
      content: "line4",
    };

    // const spy = jest.spyOn(messageContentModule, "renderChatMessage");

    async function* streamCompletion(): AsyncGenerator<ChatMessage> {
      yield messageChunk1;
      yield messageChunk2;
      yield messageChunk3;
    }

    const resultLines: string[] = [];
    for await (const line of streamLines(streamCompletion())) {
      resultLines.push(line);
    }

    expect(resultLines).toEqual(["line1", "line2", "line3", "line4"]);
    // expect(spy).toHaveBeenCalledTimes(3);
  });

  it("should log lines if log parameter is true", async () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});

    async function* streamCompletion(): AsyncGenerator<string> {
      yield "line1\nline2\n";
      yield "line3";
    }

    const resultLines: string[] = [];
    for await (const line of streamLines(streamCompletion(), true)) {
      resultLines.push(line);
    }

    expect(resultLines).toEqual(["line1", "line2", "line3"]);
    expect(consoleSpy).toHaveBeenCalledWith(
      "Streamed lines: ",
      "line1\nline2\nline3",
    );

    consoleSpy.mockRestore();
  });
});

describe("generateLines", () => {
  it("should yield the lines provided in the array", async () => {
    const lines = ["line1", "line2", "line3"];
    const resultLines: string[] = [];

    for await (const line of generateLines(lines)) {
      resultLines.push(line);
    }

    expect(resultLines).toEqual(lines);
  });
});
