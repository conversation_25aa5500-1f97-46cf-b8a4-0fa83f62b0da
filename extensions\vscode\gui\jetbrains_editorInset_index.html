<!doctype html>
<!--Used for debugging in JetBrains with hot-reloading (see ContinuePluginToolWindowFactory.kt)-->
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/play_button.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Continue</title>
  </head>
  <body>
    <div id="root"></div>

    <script type="module">
      import RefreshRuntime from "http://localhost:5173/@react-refresh";
      RefreshRuntime.injectIntoGlobalHook(window);
      window.$RefreshReg$ = () => {};
      window.$RefreshSig$ = () => (type) => type;
      window.__vite_plugin_react_preamble_installed__ = true;
    </script>

    <script>
      localStorage.setItem("ide", "jetbrains");
    </script>
    <script type="module" src="/src/editorInset/main.tsx"></script>
  </body>
</html>
