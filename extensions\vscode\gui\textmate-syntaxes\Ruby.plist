<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>comment</key>
	<string>
	TODO: unresolved issues

	text:
	"p &lt;&lt;end
	print me!
	end"
	symptoms:
	not recognized as a heredoc
	solution:
	there is no way to distinguish perfectly between the &lt;&lt; operator and the start
	of a heredoc. Currently, we require assignment to recognize a heredoc. More
	refinement is possible.
	• Heredocs with indented terminators (&lt;&lt;-) are always distinguishable, however.
	• Nested heredocs are not really supportable at present

	text:
	print &lt;&lt;-'THERE'
	This is single quoted.
	The above used #{Time.now}
	THERE
	symtoms:
	From Programming Ruby p306; should be a non-interpolated heredoc.

    text:
    val?(a):p(b)
    val?'a':'b'
    symptoms:
    ':p' is recognized as a symbol.. its 2 things ':' and 'p'.
    :'b' has same problem.
    solution:
    ternary operator rule, precedence stuff, symbol rule.
    but also consider 'a.b?(:c)' ??
</string>
	<key>fileTypes</key>
	<array>
		<string>rb</string>
		<string>rbx</string>
		<string>rjs</string>
		<string>Rakefile</string>
		<string>rake</string>
		<string>cgi</string>
		<string>fcgi</string>
		<string>gemspec</string>
		<string>irbrc</string>
		<string>Capfile</string>
		<string>ru</string>
		<string>prawn</string>
		<string>Cheffile</string>
		<string>Gemfile</string>
		<string>Guardfile</string>
		<string>Hobofile</string>
		<string>Vagrantfile</string>
		<string>Appraisals</string>
		<string>Rantfile</string>
		<string>Berksfile</string>
		<string>Berksfile.lock</string>
		<string>Thorfile</string>
		<string>Puppetfile</string>
	</array>
	<key>firstLineMatch</key>
	<string>^#!/.*\bruby</string>
	<key>keyEquivalent</key>
	<string>^~R</string>
	<key>name</key>
	<string>Ruby</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.class.ruby</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.ruby</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.other.ruby</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.ruby</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.other.ruby</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>variable.other.object.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(class)\s+(?:([.a-zA-Z0-9_:]+)(?:\s*(&lt;)\s*([.a-zA-Z0-9_:]+))?|(&lt;&lt;)\s*([.a-zA-Z0-9_:]+))</string>
			<key>name</key>
			<string>meta.class.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.module.ruby</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.module.ruby</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.module.first.ruby</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.inheritance.ruby</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.module.second.ruby</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.inheritance.ruby</string>
				</dict>
				<key>7</key>
				<dict>
					<key>name</key>
					<string>entity.other.inherited-class.module.third.ruby</string>
				</dict>
				<key>8</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.inheritance.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(module)\s+(([A-Z]\w*(::))?([A-Z]\w*(::))?([A-Z]\w*(::))*[A-Z]\w*)</string>
			<key>name</key>
			<string>meta.module.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>else if is a common mistake carried over from other languages. it works if you put in a second end, but it’s never what you want.</string>
			<key>match</key>
			<string>(?&lt;!\.)\belse(\s)+if\b</string>
			<key>name</key>
			<string>invalid.deprecated.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.constant.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>symbols as hash key (1.9 syntax)</string>
			<key>match</key>
			<string>(?&gt;[a-zA-Z_]\w*(?&gt;[?!])?)(:)(?!:)</string>
			<key>name</key>
			<string>constant.other.symbol.hashkey.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.constant.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>symbols as hash key (1.8 syntax)</string>
			<key>match</key>
			<string>(?&lt;!:)(:)(?&gt;[a-zA-Z_]\w*(?&gt;[?!])?)(?=\s*=&gt;)</string>
			<key>name</key>
			<string>constant.other.symbol.hashkey.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>everything being a reserved word, not a value and needing a 'end' is a..</string>
			<key>match</key>
			<string>(?&lt;!\.)\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\b(?![?!])</string>
			<key>name</key>
			<string>keyword.control.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>contextual smart pair support for block parameters</string>
			<key>match</key>
			<string>(?&lt;!\.)\bdo\b\s*</string>
			<key>name</key>
			<string>keyword.control.start-block.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>contextual smart pair support</string>
			<key>match</key>
			<string>(?&lt;=\{)(\s+)</string>
			<key>name</key>
			<string>meta.syntax.ruby.start-block</string>
		</dict>
		<dict>
			<key>comment</key>
			<string> as above, just doesn't need a 'end' and does a logic operation</string>
			<key>match</key>
			<string>(?&lt;!\.)\b(and|not|or)\b</string>
			<key>name</key>
			<string>keyword.operator.logical.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string> just as above but being not a logical operation</string>
			<key>match</key>
			<string>(?&lt;!\.)\b(alias|alias_method|break|next|redo|retry|return|super|undef|yield)\b(?![?!])|\bdefined\?|\bblock_given\?</string>
			<key>name</key>
			<string>keyword.control.pseudo-method.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(nil|true|false)\b(?![?!])</string>
			<key>name</key>
			<string>constant.language.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(__(FILE|LINE)__|self)\b(?![?!])</string>
			<key>name</key>
			<string>variable.language.ruby</string>
		</dict>
		<dict>
			<key>comment</key>
			<string> everything being a method but having a special function is a..</string>
			<key>match</key>
			<string>\b(initialize|new|loop|include|extend|prepend|fail|raise|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|module_function|public|protected)\b(?![?!])</string>
			<key>name</key>
			<string>keyword.other.special-method.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(require|require_relative|gem)\b</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.special-method.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>$|(?=#|\})</string>
			<key>name</key>
			<string>meta.require.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.variable.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@)[a-zA-Z_]\w*</string>
			<key>name</key>
			<string>variable.other.readwrite.instance.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.variable.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(@@)[a-zA-Z_]\w*</string>
			<key>name</key>
			<string>variable.other.readwrite.class.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.variable.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\$)[a-zA-Z_]\w*</string>
			<key>name</key>
			<string>variable.other.readwrite.global.ruby</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.variable.ruby</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\$)(!|@|&amp;|`|'|\+|\d+|~|=|/|\\|,|;|\.|&lt;|&gt;|_|\*|\$|\?|:|"|-[0adFiIlpv])</string>
			<key>name</key>
			<string>variable.other.readwrite.global.pre-defined.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(ENV)\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.other.constant.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>name</key>
			<string>meta.environment-variable.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>\b[A-Z]\w*(?=((\.|::)[A-Za-z]|\[))</string>
			<key>name</key>
			<string>support.class.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b[A-Z]\w*\b</string>
			<key>name</key>
			<string>variable.other.constant.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?x)
			         (?=def\b)                                                      # an optimization to help Oniguruma fail fast
			         (?&lt;=^|\s)(def)\s+                                              # the def keyword
			         ( (?&gt;[a-zA-Z_]\w*(?&gt;\.|::))?                                   # a method name prefix
			           (?&gt;[a-zA-Z_]\w*(?&gt;[?!]|=(?!&gt;))?                              # the method name
			           |===?|&gt;[&gt;=]?|&lt;=&gt;|&lt;[&lt;=]?|[%&amp;`/\|]|\*\*?|=?~|[-+]@?|\[\]=?) )  # …or an operator method
			         \s*(\()                                                        # the openning parenthesis for arguments
			        </string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.def.ruby</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.ruby</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>the method pattern comes from the symbol pattern, see there for a explaination</string>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.function.method.with-arguments.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?=[&amp;*_a-zA-Z])</string>
					<key>end</key>
					<string>(?=[,)])</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>storage.type.variable.ruby</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>variable.parameter.function.ruby</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\G([&amp;*]?)([_a-zA-Z][_a-zA-Z0-9]*)</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?x)
			         (?=def\b)                                                      # an optimization to help Oniguruma fail fast
			         (?&lt;=^|\s)(def)\s+                                              # the def keyword
			         ( (?&gt;[a-zA-Z_]\w*(?&gt;\.|::))?                                   # a method name prefix
			           (?&gt;[a-zA-Z_]\w*(?&gt;[?!]|=(?!&gt;))?                              # the method name
			           |===?|&gt;[&gt;=]?|&lt;=&gt;|&lt;[&lt;=]?|[%&amp;`/\|]|\*\*?|=?~|[-+]@?|\[\]=?) )  # …or an operator method
			         [ \t]                                                          # the space separating the arguments
			         (?=[ \t]*[^\s#;])                                              # make sure arguments and not a comment follow
			        </string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.def.ruby</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>same as the previous rule, but without parentheses around the arguments</string>
			<key>end</key>
			<string>$</string>
			<key>name</key>
			<string>meta.function.method.with-arguments.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?![\s,])</string>
					<key>end</key>
					<string>(?=,|$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.variable.ruby</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\G([&amp;*]?)[_a-zA-Z][_a-zA-Z0-9]*</string>
							<key>name</key>
							<string>variable.parameter.function.ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.def.ruby</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string> the optional name is just to catch the def also without a method-name</string>
			<key>match</key>
			<string>(?x)
			         (?=def\b)                                                           # an optimization to help Oniguruma fail fast
			         (?&lt;=^|\s)(def)\b                                                    # the def keyword
			         ( \s+                                                               # an optional group of whitespace followed by…
			           ( (?&gt;[a-zA-Z_]\w*(?&gt;\.|::))?                                      # a method name prefix
			             (?&gt;[a-zA-Z_]\w*(?&gt;[?!]|=(?!&gt;))?                                 # the method name
			             |===?|&gt;[&gt;=]?|&lt;=&gt;|&lt;[&lt;=]?|[%&amp;`/\|]|\*\*?|=?~|[-+]@?|\[\]=?) ) )?  # …or an operator method
			        </string>
			<key>name</key>
			<string>meta.function.method.without-arguments.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b\d(?&gt;_?\d)*(?=\.\d|[eE])(\.\d(?&gt;_?\d)*)?([eE][-+]?\d(?&gt;_?\d)*)?r?i?\b</string>
			<key>name</key>
			<string>constant.numeric.float.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(0[dD]\d|[1-9])(?&gt;_?\d)*r?i?\b</string>
			<key>name</key>
			<string>constant.numeric.integer.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b0[xX]\h(?&gt;_?\h)*r?i?\b</string>
			<key>name</key>
			<string>constant.numeric.hex.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b0[bB][01](?&gt;_?[01])*r?i?\b</string>
			<key>name</key>
			<string>constant.numeric.binary.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b0([oO]?[0-7](?&gt;_?[0-7])*)?r?i?\b</string>
			<key>name</key>
			<string>constant.numeric.octal.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>:'</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.constant.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>name</key>
			<string>constant.other.symbol.single-quoted.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\['\\]</string>
					<key>name</key>
					<string>constant.character.escape.ruby</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>:"</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.constant.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>name</key>
			<string>constant.other.symbol.double-quoted.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>comment</key>
			<string>Needs higher precidence than regular expressions.</string>
			<key>match</key>
			<string>(?&lt;!\()/=</string>
			<key>name</key>
			<string>keyword.operator.assignment.augmented.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>single quoted string (does not allow interpolation)</string>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\'|\\\\</string>
					<key>name</key>
					<string>constant.character.escape.ruby</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>double quoted string (allows for interpolation)</string>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>`</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>execute string (allows for interpolation)</string>
			<key>end</key>
			<string>`</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>include</key>
			<string>#percent_literals</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?x)
			   (?:
			     ^                      # beginning of line
			   | (?&lt;=                   # or look-behind on:
			       [=&gt;~(?:\[,|&amp;;]
			     | [\s;]if\s			# keywords
			     | [\s;]elsif\s
			     | [\s;]while\s
			     | [\s;]unless\s
			     | [\s;]when\s
			     | [\s;]assert_match\s
			     | [\s;]or\s			# boolean opperators
			     | [\s;]and\s
			     | [\s;]not\s
			     | [\s.]index\s			# methods
			     | [\s.]scan\s
			     | [\s.]sub\s
			     | [\s.]sub!\s
			     | [\s.]gsub\s
			     | [\s.]gsub!\s
			     | [\s.]match\s
			     )
			   | (?&lt;=                  # or a look-behind with line anchor:
			        ^when\s            # duplication necessary due to limits of regex
			      | ^if\s
			      | ^elsif\s
			      | ^while\s
			      | ^unless\s
			      )
			   )
			   \s*((/))(?![*+{}?])
			</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.classic.ruby</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>regular expressions (normal)
			we only start a regexp if the character before it (excluding whitespace)
			is what we think is before a regexp
			</string>
			<key>contentName</key>
			<string>string.regexp.classic.ruby</string>
			<key>end</key>
			<string>((/[eimnosux]*))</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#regex_sub</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.constant.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>symbols</string>
			<key>match</key>
			<string>(?&lt;!:)(:)(?&gt;[a-zA-Z_]\w*(?&gt;[?!]|=(?![&gt;=]))?|===?|&gt;[&gt;=]?|&lt;[&lt;=]?|&lt;=&gt;|[%&amp;`/\|]|\*\*?|=?~|[-+]@?|\[\]=?|@@?[a-zA-Z_]\w*)</string>
			<key>name</key>
			<string>constant.other.symbol.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^=begin</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>multiline comments</string>
			<key>end</key>
			<string>^=end</string>
			<key>name</key>
			<string>comment.block.documentation.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=#)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>#</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.number-sign.ruby</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>comment</key>
			<string>
			matches questionmark-letters.

			examples (1st alternation = hex):
			?\x1     ?\x61

			examples (2nd alternation = octal):
			?\0      ?\07     ?\017

			examples (3rd alternation = escaped):
			?\n      ?\b

			examples (4th alternation = meta-ctrl):
			?\C-a    ?\M-a    ?\C-\M-\C-\M-a

			examples (4th alternation = normal):
			?a       ?A       ?0
			?*       ?"       ?(
			?.       ?#


			the negative lookbehind prevents against matching
			p(42.tainted?)
			</string>
			<key>match</key>
			<string>(?&lt;!\w)\?(\\(x\h{1,2}(?!\h)\b|0[0-7]{0,2}(?![0-7])\b|[^x0MC])|(\\[MC]-)+\w|[^\s\\])</string>
			<key>name</key>
			<string>constant.numeric.ruby</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^__END__\n</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.program-block.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>__END__ marker</string>
			<key>contentName</key>
			<string>text.plain</string>
			<key>end</key>
			<string>(?=not)impossible</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?=&lt;?xml|&lt;(?i:html\b)|!DOCTYPE (?i:html\b))</string>
					<key>end</key>
					<string>(?=not)impossible</string>
					<key>name</key>
					<string>text.html.embedded.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.html.basic</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)HTML)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded html</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.html</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)HTML)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>text.html</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>text.html.basic</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)SQL)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded sql</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.sql</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)SQL)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.sql</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.sql</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)CSS)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded css</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)CSS)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.css</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.css</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)CPP)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded c++</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.cpp</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)CPP)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.cpp</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.cpp</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)C)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded c</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.c</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)C)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.c</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.c</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)(?:JS|JAVASCRIPT))\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded javascript</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.js</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)(?:JS|JAVASCRIPT))\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.js</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.js</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)JQUERY)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded jQuery javascript</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.js.jquery</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)JQUERY)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.js.jquery</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.js.jquery</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)(?:SH|SHELL))\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded shell</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.shell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)(?:SH|SHELL))\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.shell</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.shell</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)LUA)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded lua</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.lua</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)LUA)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.lua</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.lua</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)RUBY)\b\1))</string>
			<key>comment</key>
			<string>Heredoc with embedded ruby</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.embedded.block.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&gt;&lt;&lt;[-~]("?)((?:[_\w]+_|)RUBY)\b\1)</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.ruby</string>
					<key>end</key>
					<string>\s*\2$\n?</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.unquoted.heredoc.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#heredoc</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>source.ruby</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&gt;\=\s*&lt;&lt;(\w+))</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\1$</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.unquoted.heredoc.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#heredoc</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&gt;&lt;&lt;[-~](\w+))</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ruby</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>heredoc with indented terminator</string>
			<key>end</key>
			<string>\s*\1$</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ruby</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.unquoted.heredoc.ruby</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#heredoc</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;=\{|do|\{\s|do\s)(\|)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.variable.ruby</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\|)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>[_a-zA-Z][_a-zA-Z0-9]*</string>
					<key>name</key>
					<string>variable.other.block.ruby</string>
				</dict>
				<dict>
					<key>match</key>
					<string>,</string>
					<key>name</key>
					<string>punctuation.separator.variable.ruby</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>=&gt;</string>
			<key>name</key>
			<string>punctuation.separator.key-value</string>
		</dict>
		<dict>
			<key>match</key>
			<string>&lt;&lt;=|%=|&amp;=|\*=|\*\*=|\+=|\-=|\^=|\|{1,2}=|&lt;&lt;</string>
			<key>name</key>
			<string>keyword.operator.assignment.augmented.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>&lt;=&gt;|&lt;(?!&lt;|=)|&gt;(?!&lt;|=|&gt;)|&lt;=|&gt;=|===|==|=~|!=|!~|(?&lt;=[ \t])\?</string>
			<key>name</key>
			<string>keyword.operator.comparison.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;=[ \t])!+|\bnot\b|&amp;&amp;|\band\b|\|\||\bor\b|\^</string>
			<key>name</key>
			<string>keyword.operator.logical.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(%|&amp;|\*\*|\*|\+|\-|/)</string>
			<key>name</key>
			<string>keyword.operator.arithmetic.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>=</string>
			<key>name</key>
			<string>keyword.operator.assignment.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\||~|&gt;&gt;</string>
			<key>name</key>
			<string>keyword.operator.other.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>:</string>
			<key>name</key>
			<string>punctuation.separator.other.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\;</string>
			<key>name</key>
			<string>punctuation.separator.statement.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>,</string>
			<key>name</key>
			<string>punctuation.separator.object.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\.|::</string>
			<key>name</key>
			<string>punctuation.separator.method.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\{</string>
			<key>name</key>
			<string>punctuation.section.scope.begin.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\}</string>
			<key>name</key>
			<string>punctuation.section.scope.end.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\[</string>
			<key>name</key>
			<string>punctuation.section.array.begin.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\]</string>
			<key>name</key>
			<string>punctuation.section.array.end.ruby</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\(|\)</string>
			<key>name</key>
			<string>punctuation.section.function.ruby</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>escaped_char</key>
		<dict>
			<key>match</key>
			<string>\\(?:[0-7]{1,3}|x[\da-fA-F]{1,2}|.)</string>
			<key>name</key>
			<string>constant.character.escape.ruby</string>
		</dict>
		<key>heredoc</key>
		<dict>
			<key>begin</key>
			<string>^&lt;&lt;[-~]?\w+</string>
			<key>end</key>
			<string>$</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>interpolated_ruby</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>#\{</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.embedded.begin.ruby</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>source.ruby</string>
					<key>end</key>
					<string>(\})</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.embedded.end.ruby</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>source.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.embedded.line.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#nest_curly_and_self</string>
						</dict>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>nest_curly_and_self</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>punctuation.section.scope.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#nest_curly_and_self</string>
										</dict>
									</array>
								</dict>
								<dict>
									<key>include</key>
									<string>$self</string>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.ruby</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#@)[a-zA-Z_]\w*</string>
					<key>name</key>
					<string>variable.other.readwrite.instance.ruby</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.ruby</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#@@)[a-zA-Z_]\w*</string>
					<key>name</key>
					<string>variable.other.readwrite.class.ruby</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.ruby</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#\$)[a-zA-Z_]\w*</string>
					<key>name</key>
					<string>variable.other.readwrite.global.ruby</string>
				</dict>
			</array>
		</dict>
		<key>percent_literals</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>%i(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.array.symbol.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#symbol</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\&lt;|\\&gt;</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\{|\\\}</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\[|\\\]</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\(|\\\)</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>symbol</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\\|\\[ ]</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\S\w*</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%I(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.array.symbol.interpolated.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#symbol</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#symbol</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.other.symbol.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#symbol</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>symbol</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>(?=\\|#\{)</string>
									<key>end</key>
									<string>(?!\G)</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#escaped_char</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#interpolated_ruby</string>
										</dict>
									</array>
								</dict>
								<dict>
									<key>match</key>
									<string>\S\w*</string>
									<key>name</key>
									<string>constant.other.symbol.ruby</string>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%q(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.other.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
							</array>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\&lt;|\\&gt;|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\{|\\\}|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\[|\\\]|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\(|\\\)|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%Q?(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.other.interpolated.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%r(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>([)\]}&gt;]|\1\2)[eimnosux]*</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.regexp.percent.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#regex_sub</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#regex_sub</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#regex_sub</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#regex_sub</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#regex_sub</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%s(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>constant.other.symbol.percent.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
							</array>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\&lt;|\\&gt;|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\{|\\\}|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\[|\\\]|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\\\(|\\\)|\\\\</string>
									<key>name</key>
									<string>constant.character.escape.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%w(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.array.string.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#string</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\&lt;|\\&gt;</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\{|\\\}</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\[|\\\]</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\(|\\\)</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>string</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>constant.character.escape.ruby</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\\\\|\\[ ]</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
								<dict>
									<key>match</key>
									<string>\S\w*</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%W(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.array.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.array.string.interpolated.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#string</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#string</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>captures</key>
									<dict>
										<key>0</key>
										<dict>
											<key>name</key>
											<string>string.other.ruby</string>
										</dict>
									</dict>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#string</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>string</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>begin</key>
									<string>(?=\\|#\{)</string>
									<key>end</key>
									<string>(?!\G)</string>
									<key>name</key>
									<string>string.other.ruby</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#escaped_char</string>
										</dict>
										<dict>
											<key>include</key>
											<string>#interpolated_ruby</string>
										</dict>
									</array>
								</dict>
								<dict>
									<key>match</key>
									<string>\S\w*</string>
									<key>name</key>
									<string>string.other.ruby</string>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>begin</key>
					<string>%x(?:([(\[{&lt;])|([^\w\s]|_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>[)\]}&gt;]|\1\2</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.interpolated.percent.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\()(?!\))</string>
							<key>end</key>
							<string>(?=\))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parens</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\[)(?!\])</string>
							<key>end</key>
							<string>(?=\])</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#brackets</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=\{)(?!\})</string>
							<key>end</key>
							<string>(?=\})</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#braces</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\G(?&lt;=&lt;)(?!&gt;)</string>
							<key>end</key>
							<string>(?=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#angles</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#interpolated_ruby</string>
						</dict>
					</array>
					<key>repository</key>
					<dict>
						<key>angles</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>&lt;</string>
									<key>end</key>
									<string>&gt;</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#angles</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>braces</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\{</string>
									<key>end</key>
									<string>\}</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#braces</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>brackets</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\[</string>
									<key>end</key>
									<string>\]</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#brackets</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
						<key>parens</key>
						<dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#interpolated_ruby</string>
								</dict>
								<dict>
									<key>begin</key>
									<string>\(</string>
									<key>end</key>
									<string>\)</string>
									<key>patterns</key>
									<array>
										<dict>
											<key>include</key>
											<string>#parens</string>
										</dict>
									</array>
								</dict>
							</array>
						</dict>
					</dict>
				</dict>
			</array>
		</dict>
		<key>regex_sub</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#interpolated_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.quantifier.begin.ruby</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.quantifier.end.ruby</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\{)\d+(,\d+)?(\})</string>
					<key>name</key>
					<string>keyword.operator.quantifier.ruby</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\[(?:\^?\])?</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character-class.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\]</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character-class.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>constant.other.character-class.set.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(\?#</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.begin.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.end.ruby</string>
						</dict>
					</dict>
					<key>name</key>
					<string>comment.line.number-sign.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.group.ruby</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>meta.group.regexp.ruby</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#regex_sub</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?&lt;=^|\s)(#)\s(?=[[a-zA-Z0-9,. \t?!-][^\x{00}-\x{7F}]]*$)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.ruby</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>We are restrictive in what we allow to go after the comment character to avoid false positives, since the availability of comments depend on regexp flags.</string>
					<key>end</key>
					<string>$\n?</string>
					<key>name</key>
					<string>comment.line.number-sign.ruby</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.ruby</string>
	<key>uuid</key>
	<string>E00B62AC-6B1C-11D9-9B1F-000D93589AF6</string>
</dict>
</plist>

