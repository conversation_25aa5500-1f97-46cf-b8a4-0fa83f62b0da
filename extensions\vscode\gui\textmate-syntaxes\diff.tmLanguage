<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>patch</string>
		<string>diff</string>
		<string>rej</string>
	</array>
	<key>firstLineMatch</key>
	<string>(?x)^
		(===\ modified\ file
		|==== \s* // .+ \s - \s .+ \s+ ====
		|Index:\ 
		|---\ [^%\n]
		|\*\*\*.*\d{4}\s*$
		|\d+(,\d+)* (a|d|c) \d+(,\d+)* $
		|diff\ --git\ 
		|commit\ [0-9a-f]{40}$
		)</string>
	<key>keyEquivalent</key>
	<string>^~D</string>
	<key>name</key>
	<string>Diff</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.separator.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^((\*{15})|(={67})|(-{3}))$\n?</string>
			<key>name</key>
			<string>meta.separator.diff</string>
		</dict>
		<dict>
			<key>match</key>
			<string>^\d+(,\d+)*(a|d|c)\d+(,\d+)*$\n?</string>
			<key>name</key>
			<string>meta.diff.range.normal</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>meta.toc-list.line-number.diff</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(@@)\s*(.+?)\s*(@@)($\n?)?</string>
			<key>name</key>
			<string>meta.diff.range.unified</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
				<key>7</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.range.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(((\-{3}) .+ (\-{4}))|((\*{3}) .+ (\*{4})))$\n?</string>
			<key>name</key>
			<string>meta.diff.range.context</string>
		</dict>
		<dict>
			<key>match</key>
			<string>^diff --git a/.*$\n?</string>
			<key>name</key>
			<string>meta.diff.header.git</string>
		</dict>
		<dict>
			<key>match</key>
			<string>^diff (-|\S+\s+\S+).*$\n?</string>
			<key>name</key>
			<string>meta.diff.header.command</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.from-file.diff</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.from-file.diff</string>
				</dict>
				<key>7</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.from-file.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(^(((-{3}) .+)|((\*{3}) .+))$\n?|^(={4}) .+(?= - ))</string>
			<key>name</key>
			<string>meta.diff.header.from-file</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.to-file.diff</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.to-file.diff</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.to-file.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(^(\+{3}) .+$\n?| (-) .* (={4})$\n?)</string>
			<key>name</key>
			<string>meta.diff.header.to-file</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.inserted.diff</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.inserted.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(((&gt;)( .*)?)|((\+).*))$\n?</string>
			<key>name</key>
			<string>markup.inserted.diff</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.inserted.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(!).*$\n?</string>
			<key>name</key>
			<string>markup.changed.diff</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.inserted.diff</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.inserted.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(((&lt;)( .*)?)|((-).*))$\n?</string>
			<key>name</key>
			<string>markup.deleted.diff</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^(#)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.diff</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Git produces unified diffs with embedded comments"</string>
			<key>end</key>
			<string>\n</string>
			<key>name</key>
			<string>comment.line.number-sign.diff</string>
		</dict>
		<dict>
			<key>match</key>
			<string>^index [0-9a-f]{7,40}\.\.[0-9a-f]{7,40}.*$\n?</string>
			<key>name</key>
			<string>meta.diff.index.git</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.key-value.diff</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>meta.toc-list.file-name.diff</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^Index(:) (.+)$\n?</string>
			<key>name</key>
			<string>meta.diff.index</string>
		</dict>
		<dict>
			<key>match</key>
			<string>^Only in .*: .*$\n?</string>
			<key>name</key>
			<string>meta.diff.only-in</string>
		</dict>
	</array>
	<key>scopeName</key>
	<string>source.diff</string>
	<key>uuid</key>
	<string>7E848FF4-708E-11D9-97B4-0011242E4184</string>
</dict>
</plist>
