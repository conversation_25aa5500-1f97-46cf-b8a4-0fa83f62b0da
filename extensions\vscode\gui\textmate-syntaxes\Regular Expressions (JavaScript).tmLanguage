<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array/>
	<key>hideFromUser</key>
	<true/>
	<key>name</key>
	<string>Regular Expressions (JavaScript)</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#regexp</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>regex-character-class</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\[wWsSdD]|\.</string>
					<key>name</key>
					<string>constant.character.character-class.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\([0-7]{3}|x\h\h|u\h\h\h\h)</string>
					<key>name</key>
					<string>constant.character.numeric.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\c[A-Z]</string>
					<key>name</key>
					<string>constant.character.control.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.backslash.regexp</string>
				</dict>
			</array>
		</dict>
		<key>regexp</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\[bB]|\^|\$</string>
					<key>name</key>
					<string>keyword.control.anchor.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\[1-9]\d*</string>
					<key>name</key>
					<string>keyword.other.back-reference.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>[?+*]|\{(\d+,\d+|\d+,|,\d+|\d+)\}\??</string>
					<key>name</key>
					<string>keyword.operator.quantifier.regexp</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\|</string>
					<key>name</key>
					<string>keyword.operator.or.regexp</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(\()((\?=)|(\?!))</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.group.regexp</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>meta.assertion.look-ahead.regexp</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>meta.assertion.negative-look-ahead.regexp</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.group.regexp</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.group.assertion.regexp</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#regexp</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\((\?:)?</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.group.regexp</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.group.regexp</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.group.regexp</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#regexp</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(\[)(\^)?</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character-class.regexp</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.negation.regexp</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\])</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character-class.regexp</string>
						</dict>
					</dict>
					<key>name</key>
					<string>constant.other.character-class.set.regexp</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>constant.character.numeric.regexp</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>constant.character.control.regexp</string>
								</dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>constant.character.escape.backslash.regexp</string>
								</dict>
								<key>4</key>
								<dict>
									<key>name</key>
									<string>constant.character.numeric.regexp</string>
								</dict>
								<key>5</key>
								<dict>
									<key>name</key>
									<string>constant.character.control.regexp</string>
								</dict>
								<key>6</key>
								<dict>
									<key>name</key>
									<string>constant.character.escape.backslash.regexp</string>
								</dict>
							</dict>
							<key>match</key>
							<string>(?:.|(\\(?:[0-7]{3}|x\h\h|u\h\h\h\h))|(\\c[A-Z])|(\\.))\-(?:[^\]\\]|(\\(?:[0-7]{3}|x\h\h|u\h\h\h\h))|(\\c[A-Z])|(\\.))</string>
							<key>name</key>
							<string>constant.other.character-class.range.regexp</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#regex-character-class</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#regex-character-class</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.js.regexp</string>
	<key>uuid</key>
	<string>AC8679DE-3AC7-4056-84F9-69A7ADC29DDD</string>
</dict>
</plist>
