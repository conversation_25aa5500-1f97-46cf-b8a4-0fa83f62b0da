<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>clj</string>
		<string>cljs</string>
		<string>clojure</string>
	</array>
	<key>foldingStartMarker</key>
	<string>\(\s*$</string>
	<key>foldingStopMarker</key>
	<string>^\s*\)</string>
	<key>keyEquivalent</key>
	<string>^~C</string>
	<key>name</key>
	<string>Clojure</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#comment</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#shebang-comment</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#qouted-sexp</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#sexp</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#keyfn</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#string</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#vector</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#set</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#map</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#regexp</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#var</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#constants</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#symbol</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#whitespace</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>comment</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.clojure</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(;).*$\n?</string>
			<key>name</key>
			<string>comment.line.semicolon.clojure</string>
		</dict>
		<key>constants</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>(nil)(?=(\s|\)|\]|\}))</string>
					<key>name</key>
					<string>constant.language.nil.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(true|false)</string>
					<key>name</key>
					<string>constant.language.boolean.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\d+/\d+)</string>
					<key>name</key>
					<string>constant.numeric.ratio.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\d+r\d+)</string>
					<key>name</key>
					<string>constant.numeric.arbitrary-radix.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(0x\d+)</string>
					<key>name</key>
					<string>constant.numeric.hexidecimal.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(0\d+)</string>
					<key>name</key>
					<string>constant.numeric.octal.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\d+)</string>
					<key>name</key>
					<string>constant.numeric.decimal.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(?&lt;=(\s|\(|\[|\{)):[a-zA-Z0-9\#\.\-\_\:\+\=\&gt;\&lt;\/\!\?\*]+(?=(\s|\)|\]|\}))</string>
					<key>name</key>
					<string>constant.keyword.clojure</string>
				</dict>
			</array>
		</dict>
		<key>keyfn</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>(?&lt;=(\s|\(|\[|\{))(if(-[-a-z\?]*)?|when(-[-a-z]*)?|for(-[-a-z]*)?|cond|do|let(-[-a-z\?]*)?|binding|loop|recur|fn|throw[a-z\-]*|try|catch|finally|([a-z]*case))(?=(\s|\)|\]|\}))</string>
					<key>name</key>
					<string>storage.control.clojure</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(?&lt;=(\s|\(|\[|\{))(declare-?|(in-)?ns|import|use|require|load|compile|(def[a-z\-]*))(?=(\s|\)|\]|\}))</string>
					<key>name</key>
					<string>keyword.control.clojure</string>
				</dict>
			</array>
		</dict>
		<key>map</key>
		<dict>
			<key>begin</key>
			<string>(\{)</string>
			<key>end</key>
			<string>(\})</string>
			<key>name</key>
			<string>meta.map.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>qouted-sexp</key>
		<dict>
			<key>begin</key>
			<string>(['``]\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.begin.clojure</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\))(\n)?</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.end.clojure</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>meta.after-expression.clojure</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.qouted-expression.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>regexp</key>
		<dict>
			<key>begin</key>
			<string>#\"</string>
			<key>end</key>
			<string>\"</string>
			<key>name</key>
			<string>string.regexp.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#regexp_escaped_char</string>
				</dict>
			</array>
		</dict>
		<key>regexp_escaped_char</key>
		<dict>
			<key>match</key>
			<string>\\(\")</string>
			<key>name</key>
			<string>string.regexp.clojure</string>
		</dict>
		<key>set</key>
		<dict>
			<key>begin</key>
			<string>(\#\{)</string>
			<key>end</key>
			<string>(\})</string>
			<key>name</key>
			<string>meta.set.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>sexp</key>
		<dict>
			<key>begin</key>
			<string>(\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.begin.clojure</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\))(\n)?</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.end.clojure</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>meta.after-expression.clojure</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.expression.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?&lt;=\()(ns|def|def-|defn|defn-|defvar|defvar-|defmacro|defmacro-|deftest)\s+(.+?)(?=\s)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.clojure</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.global.clojure</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\))</string>
					<key>name</key>
					<string>meta.definition.global.clojure</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>shebang-comment</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.shebang.clojure</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(\#!).*$\n?</string>
			<key>name</key>
			<string>comment.line.semicolon.clojure</string>
		</dict>
		<key>string</key>
		<dict>
			<key>begin</key>
			<string>(")</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.clojure</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(")</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.clojure</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.clojure</string>
				</dict>
			</array>
		</dict>
		<key>symbol</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>(\w[\w\d]+)</string>
					<key>name</key>
					<string>meta.symbol.clojure</string>
				</dict>
			</array>
		</dict>
		<key>var</key>
		<dict>
			<key>match</key>
			<string>(?&lt;=(\s|\(|\[|\{)\#)'[a-zA-Z0-9\.\-\_\:\+\=\&gt;\&lt;\/\!\?\*]+(?=(\s|\)|\]|\}))</string>
			<key>name</key>
			<string>meta.var.clojure</string>
		</dict>
		<key>vector</key>
		<dict>
			<key>begin</key>
			<string>(\[)</string>
			<key>end</key>
			<string>(\])</string>
			<key>name</key>
			<string>meta.vector.clojure</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>whitespace</key>
		<dict>
			<key>match</key>
			<string>\s+$</string>
			<key>name</key>
			<string>invalid.trailing-whitespace</string>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.clojure</string>
	<key>smartTypingPairs</key>
	<array>
		<array>
			<string>"</string>
			<string>"</string>
		</array>
		<array>
			<string>(</string>
			<string>)</string>
		</array>
		<array>
			<string>{</string>
			<string>}</string>
		</array>
		<array>
			<string>[</string>
			<string>]</string>
		</array>
	</array>
	<key>uuid</key>
	<string>6A87759F-F746-4E84-B788-965B46363202</string>
</dict>
</plist>