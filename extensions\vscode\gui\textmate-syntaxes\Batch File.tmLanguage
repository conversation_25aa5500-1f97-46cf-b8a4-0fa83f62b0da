<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>uuid</key>
        <string>E07EC438-7B75-4437-8AA1-DA94C1E6EACC</string>
        <key>patterns</key>
        <array>
            <dict>
                <key>name</key>
                <string>keyword.command.dosbatch</string>
                <key>match</key>
                <string>\b(?i)(?:append|assoc|at|attrib|break|cacls|cd|chcp|chdir|chkdsk|chkntfs|cls|cmd|color|comp|compact|convert|copy|date|del|dir|diskcomp|diskcopy|doskey|echo|endlocal|erase|fc|find|findstr|format|ftype|graftabl|help|keyb|label|md|mkdir|mode|more|move|path|pause|popd|print|prompt|pushd|rd|recover|ren|rename|replace|restore|rmdir|set|setlocal|shift|sort|start|subst|time|title|tree|type|ver|verify|vol|xcopy)\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.control.statement.dosbatch</string>
                <key>match</key>
                <string>\b(?i)(?:goto|call|exit)\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.control.conditional.if.dosbatch</string>
                <key>match</key>
                <string>\b(?i)if\s+((not)\s+)(exist|defined|errorlevel|cmdextversion)\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.control.conditional.dosbatch</string>
                <key>match</key>
                <string>\b(?i)(?:if|else)\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.control.repeat.dosbatch</string>
                <key>match</key>
                <string>\b(?i)for\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.operator.dosbatch</string>
                <key>match</key>
                <string>\b(?:EQU|NEQ|LSS|LEQ|GTR|GEQ)\b</string>
            </dict>
            <dict>
                <key>name</key>
                <string>comment.line.rem.dosbatch</string>
                <key>match</key>
                <string>\b(?i)rem(?:$|\s.*$)</string>
            </dict>
            <dict>
                <key>name</key>
                <string>comment.line.colons.dosbatch</string>
                <key>match</key>
                <string>\s*:\s*:.*$</string>
            </dict>
            <dict>
                <key>captures</key>
                <dict>
                    <key>1</key>
                    <dict>
                        <key>name</key>
                        <string>variable.parameter.function.begin.shell</string>
                    </dict>
                </dict>
                <key>name</key>
                <string>variable.parameter.function.dosbatch</string>
                <key>match</key>
                <string>(?i)(%)(~(?:f|d|p|n|x|s|a|t|z|\$[^:]*:)*)?\d</string>
            </dict>
            <dict>
                <key>captures</key>
                <dict>
                    <key>1</key>
                    <dict>
                        <key>name</key>
                        <string>variable.parameter.loop.begin.shell</string>
                    </dict>
                </dict>
                <key>name</key>
                <string>variable.parameter.loop.dosbatch</string>
                <key>match</key>
                <string>(?i)(%%)(~(?:f|d|p|n|x|s|a|t|z|\$[^:]*:)*)?[a-z]</string>
            </dict>
            <dict>
                <key>captures</key>
                <dict>
                    <key>1</key>
                    <dict>
                        <key>name</key>
                        <string>variable.other.parsetime.begin.shell</string>
                    </dict>
                    <key>2</key>
                    <dict>
                        <key>name</key>
                        <string>variable.other.parsetime.end.shell</string>
                    </dict>
                </dict>
                <key>name</key>
                <string>variable.other.parsetime.dosbatch</string>
                <key>match</key>
                <string>(%)[^%]+(%)</string>
            </dict>
            <dict>
                <key>captures</key>
                <dict>
                    <key>1</key>
                    <dict>
                        <key>name</key>
                        <string>variable.other.delayed.begin.shell</string>
                    </dict>
                    <key>2</key>
                    <dict>
                        <key>name</key>
                        <string>variable.other.delayed.end.shell</string>
                    </dict>
                </dict>
                <key>name</key>
                <string>variable.other.delayed.dosbatch</string>
                <key>match</key>
                <string>(!)[^!]+(!)</string>
            </dict>
            <dict>
                <key>begin</key>
                <string>"</string>
                <key>endCaptures</key>
                <dict>
                    <key>0</key>
                    <dict>
                        <key>name</key>
                        <string>punctuation.definition.string.end.shell</string>
                    </dict>
                </dict>
                <key>beginCaptures</key>
                <dict>
                    <key>0</key>
                    <dict>
                        <key>name</key>
                        <string>punctuation.definition.string.begin.shell</string>
                    </dict>
                </dict>
                <key>name</key>
                <string>string.quoted.double.dosbatch</string>
                <key>end</key>
                <string>"|$</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.operator.pipe.dosbatch</string>
                <key>match</key>
                <string>[|]</string>
            </dict>
            <dict>
                <key>name</key>
                <string>keyword.operator.redirect.shell</string>
                <key>match</key>
                <string>&amp;&gt;|\d*&gt;&amp;\d*|\d*(&gt;&gt;|&gt;|&lt;)|\d*&lt;&amp;|\d*&lt;&gt;</string>
            </dict>
        </array>
        <key>name</key>
        <string>Batch File</string>
        <key>scopeName</key>
        <string>source.dosbatch</string>
        <key>fileTypes</key>
        <array>
            <string>bat</string>
        </array>
    </dict>
</plist>