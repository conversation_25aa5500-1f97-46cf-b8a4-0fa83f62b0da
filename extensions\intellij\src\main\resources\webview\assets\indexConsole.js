import{b as c,j as r,d as F,e as I,h as C,F as M,f as y,k as R,R as E}from"./XCircleIcon.js";function L({title:e,titleId:s,...t},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":s},t),e?c.createElement("title",{id:s},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 9.563C9 9.252 9.252 9 9.563 9h4.874c.311 0 .563.252.563.563v4.874c0 .311-.252.563-.563.563H9.564A.562.562 0 0 1 9 14.437V9.564Z"}))}const B=c.forwardRef(L),w=32;function $(e,s){let t=e.results[e.results.length-1];t==null||t.length==w?e.results.push([s]):t.push(s)}function O(e){e.results;let s=e.results[e.results.length-1],t;return s==null||s.length==w?t=[...e.results]:(t=e.results.slice(0,-1),t.push([...s])),{...e,results:t}}function b(e,s){const t=e.interactions,n=new Map;let l=e.order,d=!1;for(const a of s){let o=n.get(a.interactionId);if(o===void 0){const i=t.get(a.interactionId);i?o=O(i):(o={results:[]},d?l.push(a.interactionId):(l=[...l,a.interactionId],d=!0)),n.set(a.interactionId,o)}switch(a.kind){case"startChat":case"startComplete":case"startFim":o.start=a;break;case"chunk":case"message":$(o,a);break;case"success":case"error":case"cancel":o.end=a;break}}for(const a of t.keys())n.has(a)||n.set(a,t.get(a));return{loading:!1,interactions:n,order:l}}function G(e,s){const t=new Map(e.interactions);t.delete(s);const n=e.order.filter(l=>l!==s);return{loading:!1,interactions:t,order:n}}function P(){const[e,s]=c.useReducer((t,n)=>{switch(n.type){case"init":return b(t,n.items);case"item":return b(t,[n.item]);case"remove":return G(t,n.interactionId);case"clear":return{loading:!1,interactions:new Map,order:[]}}},{loading:!0,interactions:new Map,order:[]});return c.useEffect(function(){const t=crypto.randomUUID(),n=l=>{l.data.uuid===t&&s(l.data)};return window.addEventListener("message",n),vscode.postMessage({type:"start",uuid:t}),()=>{vscode.postMessage({type:"stop",uuid:t}),window.removeEventListener("message",n)}},[]),e}function N(e){return c.useMemo(()=>{var h;if(e.start==null)return{result:"",type:""};const s=e.start.kind.slice(5);let t;switch((h=e.end)==null?void 0:h.kind){case"cancel":t="Cancelled";break;case"error":t="Error";break;case"success":t="Success";break;case void 0:t=""}let n,l,d,a,o,i;if(e.start!=null&&e.results.length>0){const x=e.results[0],m=x?x[0]:void 0;l=m?m.timestamp-e.start.timestamp:void 0}if(e.end!=null)n=e.end.timestamp-e.start.timestamp,a=e.end.promptTokens,o=e.end.generatedTokens,i=e.end.thinkingTokens,l!=null&&(d=(o+i)/((n-l)/1e3));else{const x=e.results[e.results.length-1],m=x?x[x.length-1]:void 0;n=m?m.timestamp-e.start.timestamp:void 0}return{result:t,type:s,totalTime:n,toFirstToken:l,tokensPerSecond:d,promptTokens:a,generatedTokens:o,thinkingTokens:i}},[e])}function A({item:e}){switch(e.kind){case"success":return r.jsx(r.Fragment,{});case"error":return r.jsxs("div",{children:[r.jsx("span",{className:"text-[color:var(--vscode-statusBarItem-errorForeground) m-0.5 inline-block rounded-sm bg-[color:var(--vscode-statusBarItem-errorBackground)] p-0.5",children:"Error"}),e.message]});case"cancel":return r.jsx("div",{children:r.jsx("span",{className:"text-[color:var(--vscode-statusBarItem-warningForeground) m-0.5 inline-block rounded-sm bg-[color:var(--vscode-statusBarItem-warningBackground)] p-0.5",children:"Cancelled"})})}}function v(e){return r.jsx("span",{className:"whitespace-pre-wrap",children:e})}function f(e){return r.jsx("div",{children:r.jsx("span",{className:"bg-[color:var(--vscode-list-inactiveSelectionBackground)] text-xs",children:e})})}function g(e){return typeof e.content=="string"?v(e.content):e.content.map(s=>s.type=="text"?v(s.text):r.jsxs("div",{children:["Image: ",s.imageUrl.url]}))}function j(e,s){switch(e.role){case"assistant":return r.jsxs(r.Fragment,{children:[s?f(e.role):"",e.toolCalls?e.toolCalls.map(t=>r.jsxs("pre",{children:["Tool call: ",JSON.stringify(t,void 0,2)]})):"",g(e)]});case"thinking":return r.jsxs(r.Fragment,{children:[s?f(e.role):"",e.toolCalls?e.toolCalls.map(t=>r.jsxs("pre",{children:["Tool call: ",JSON.stringify(t,void 0,2)]})):"",g(e),e.redactedThinking&&r.jsxs("pre",{children:["Redacted Thinking: ",e.redactedThinking]}),e.signature&&r.jsxs("pre",{children:["Signature: ",e.signature]})]});case"user":return r.jsxs(r.Fragment,{children:[s?f(e.role):"",g(e)]});case"system":return r.jsxs(r.Fragment,{children:[s?f(e.role):"",v(e.content)]});case"tool":return r.jsxs(r.Fragment,{children:[s?f(e.role):"",r.jsxs("pre",{children:["Tool Call ID: ",e.toolCallId]}),v(e.content)]})}}const D=c.memo(function({message:s}){return j(s,!0)}),H=c.memo(function({result:s,prevResult:t}){switch(s.kind){case"chunk":return r.jsx("span",{children:s.chunk});case"message":switch(s.message.role){case"assistant":case"thinking":const n=!((t==null?void 0:t.kind)==="message"&&t.message.role===s.message.role);return j(s.message,n);default:return r.jsx("div",{className:"border-[color:var(--vscode-panel-border) border-2 border-solid p-1",children:j(s.message,!0)})}break}}),J=c.memo(function({group:s}){return r.jsx(r.Fragment,{children:s.map((t,n)=>r.jsx(H,{result:t,prevResult:s[n-1]},n))})});function u({label:e,children:s}){const[t,n]=c.useState(!1);return r.jsxs("div",{children:[r.jsxs("div",{className:"text-base",onClick:()=>n(!t),children:[t?r.jsx(F,{className:"h-[16px] w-[16px]"}):r.jsx(I,{className:"h-[16px] w-[16px]"})," ",r.jsx("span",{className:"align-top text-sm font-bold",children:e})]}),t&&r.jsx("div",{children:s})]})}function U({item:e}){return r.jsx("div",{className:"border-0 border-b-2 border-solid border-[color:var(--vscode-panel-border)] p-1",children:(()=>{switch(e.kind){case"startChat":return r.jsxs(r.Fragment,{children:[r.jsx(u,{label:"Prompt",children:r.jsx("div",{className:"p-1",children:e.messages.map((s,t)=>r.jsx(D,{message:s},t))})}),r.jsx(u,{label:"Options",children:r.jsx("pre",{className:"m-0",children:JSON.stringify(e.options,void 0,2)})})]});case"startComplete":return r.jsxs(r.Fragment,{children:[r.jsx(u,{label:"Prompt",children:r.jsx("pre",{className:"m-0",children:e.prompt})}),r.jsx(u,{label:"Options",children:r.jsx("pre",{className:"m-0",children:JSON.stringify(e.options,void 0,2)})})]});case"startFim":return r.jsxs(r.Fragment,{children:[r.jsx(u,{label:"Prefix",children:r.jsx("pre",{className:"m-0",children:e.prefix})}),r.jsx(u,{label:"Suffix",children:r.jsx("pre",{className:"m-0",children:e.suffix})}),r.jsx(u,{label:"Options",children:r.jsx("pre",{className:"m-0",children:JSON.stringify(e.options,void 0,2)})})]})}})()})}function S({interaction:e}){if(e.end)switch(e.end.kind){case"success":return r.jsx(M,{className:"relative top-[2px] -mt-[2px] h-[16px] w-[16px] pr-[2px] text-[color:var(--vscode-charts-green)]"});case"cancel":return r.jsx(B,{className:"relative top-[2px] -mt-[2px] h-[16px] w-[16px] pr-[2px] text-[color:var(--vscode-list-warningForeground)]"});case"error":return r.jsx(C,{className:"relative top-[2px] -mt-[2px] h-[16px] w-[16px] pr-[2px] text-[color:var(--vscode-list-errorForeground)]"})}else return r.jsx(y,{className:"relative top-[2px] -mt-[2px] h-[16px] w-[16px] pr-[2px]"})}function T(e){return r.jsx("div",{className:"border-0 border-r-2 border-solid border-[color:var(--vscode-panel-border)] pl-2 pr-2 text-sm",children:e})}function p({label:e,value:s,format:t}){return T(s!=null?`${e}: ${t?t(s):s}`:"")}function V({children:e}){return T(e)}function k(e){return(e/1e3).toFixed(2)+"s"}function Z({interaction:e}){const s=c.useRef(null),t=c.useRef(null),n=N(e),[l,d]=c.useState(!0);function a(){const o=s.current;if(!o)return;d(o.scrollHeight-o.scrollTop-o.clientHeight<5)}return c.useEffect(()=>{var i;if(e.end)return;const o=e.results[e.results.length-1];if(o!=t.current&&(t.current=o,l)){const h=(i=s.current)==null?void 0:i.lastChild;h&&h.scrollIntoView({behavior:"auto",block:"end"})}},[e,l]),r.jsxs("div",{className:"m-0 flex min-w-0 flex-1 shrink grow flex-col",children:[r.jsxs("div",{className:"shrink-0 text-base",children:[r.jsxs("div",{className:"columns-3 gap-0 border-0 border-b-2 border-solid border-[color:var(--vscode-panel-border)] p-0",children:[r.jsx(p,{label:"Type",value:n.type}),r.jsxs(V,{children:["Result: ",r.jsx(S,{interaction:e}),n.result]})]}),r.jsxs("div",{className:"columns-3 gap-0 border-0 border-b-2 border-solid border-[color:var(--vscode-panel-border)] p-0",children:[r.jsx(p,{label:"Prompt Tokens",value:n.promptTokens}),r.jsx(p,{label:"Generated Tokens",value:n.generatedTokens}),r.jsx(p,{label:"ThinkingTokens",value:n.thinkingTokens})]}),r.jsxs("div",{className:"columns-3 gap-0 border-0 border-b-2 border-solid border-[color:var(--vscode-panel-border)] p-0",children:[r.jsx(p,{label:"Total Time",value:n.totalTime,format:k}),r.jsx(p,{label:"To First Token",value:n.toFirstToken,format:k}),r.jsx(p,{label:"Tokens/s",value:n.tokensPerSecond,format:o=>o.toFixed(1)})]})]}),r.jsxs("div",{ref:s,className:"grow overflow-auto",onScroll:a,children:[e.start?r.jsx(U,{item:e.start}):"",r.jsx("div",{className:"whitespace-pre-wrap p-2",children:e.results.map((o,i)=>r.jsx(J,{group:o},i))}),e.end?r.jsx(A,{item:e.end}):""]})]})}function _(e){const s=new Date(e),t=s.getHours().toString().padStart(2,"0"),n=s.getMinutes().toString().padStart(2,"0"),l=s.getSeconds(),d=s.getMilliseconds(),a=`${l}.${Math.floor(d/100)}`;return`${t}:${n}:${a.padStart(4,"0")}`}function z({interactionId:e,interaction:s,onClickInteraction:t,selected:n}){const l=N(s);return r.jsxs("li",{className:"w-full cursor-pointer pb-[3px] pl-[4px] pr-[4px] pt-[3px] "+(n?"bg-[color:var(--vscode-list-inactiveSelectionBackground)] text-[color:var(--vscode-list-inctiveSelectionForeground)] group-focus-within:bg-[color:var(--vscode-list-activeSelectionBackground)] group-focus-within:text-[color:var(--vscode-list-activeSelectionForeground)]":"hover:bg-[color:var(--vscode-list-inactiveSelectionBackground)]"),onClick:()=>t(e),children:[r.jsx(S,{interaction:s})," ",r.jsx("span",{className:"inline-block w-[70px]",children:s.start?_(s.start.timestamp):""}),r.jsx("span",{className:"inline-block",children:l.type})]},e)}function W({llmLog:e,onClickInteraction:s}){const t=c.useRef(null),[n,l]=c.useState(void 0),d=c.useRef(0);return c.useEffect(()=>{var a;if(e.order.length!=d.current){l(e.order[e.order.length-1]),s(e.order[e.order.length-1]),d.current=e.order.length;const o=(a=t.current)==null?void 0:a.lastChild;o&&o.scrollIntoView({behavior:"smooth",block:"end"})}}),r.jsx("ul",{tabIndex:1,ref:t,className:"group m-0 w-[150px] flex-none list-none overflow-auto border-0 border-r-2 border-solid border-[color:var(--vscode-panel-border)] p-0",children:e.order.map(a=>r.jsx(z,{interactionId:a,interaction:e.interactions.get(a),onClickInteraction:o=>{var i;(i=t.current)==null||i.focus(),l(o),s(o)},selected:a==n},a))})}function X(){const e=P(),[s,t]=c.useState(void 0),n=s?e.interactions.get(s):void 0;return e.loading?r.jsx("div",{children:"Loading..."}):r.jsxs("div",{className:"flex h-full w-full",children:[r.jsx(W,{llmLog:e,onClickInteraction:l=>{t(l)}}),n&&r.jsx(Z,{interaction:n},"{selectedId}")]})}(async()=>R.createRoot(document.getElementById("root")).render(r.jsx(E.StrictMode,{children:r.jsx(X,{})})))();
